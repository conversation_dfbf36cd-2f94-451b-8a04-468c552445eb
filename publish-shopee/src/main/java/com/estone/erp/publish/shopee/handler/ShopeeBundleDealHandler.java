package com.estone.erp.publish.shopee.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.ShopeeDeleteBundleDealtemV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeBundelDealCallV2;
import com.estone.erp.publish.shopee.component.marking.BundleDealConfigParam;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealAddStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealSuitTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal;
import com.estone.erp.publish.shopee.model.ShopeeMarketingConfig;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.shopee.util.ConnectedComponents;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishShopeeSpuCompose;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealSpuCompose;
import com.estone.erp.publish.tidb.publishtidb.service.AdsPublishShopeeSpuComposeService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealProductListingService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealSpuComposeService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ShopeeBundleDealHandler {

    @Resource
    private ShopeeBundleDealProductListingService shopeeBundleDealProductListingService;
    @Resource
    private AdsPublishShopeeSpuComposeService adsPublishShopeeSpuComposeService;
    @Resource
    private ShopeeBundleDealSpuComposeService shopeeBundleDealSpuComposeService;
    @Autowired
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;
    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;


    public String deleteBundleDeal(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, ShopeeBundleDealProductListing shopeeBundleDealProductListing) {
        Long bundleDealId = shopeeBundleDealProductListing.getBundleDealId();
        Long itemId = shopeeBundleDealProductListing.getItemId();

        ShopeeDeleteBundleDealtemV2 shopeeDeleteBundleDealtemV2 = new ShopeeDeleteBundleDealtemV2();
        shopeeDeleteBundleDealtemV2.setBundle_deal_id(bundleDealId);
        ShopeeDeleteBundleDealtemV2.ItemListDTO itemListDTO = new ShopeeDeleteBundleDealtemV2.ItemListDTO();
        itemListDTO.setItem_id(itemId);
        shopeeDeleteBundleDealtemV2.setItem_list(List.of(itemListDTO));
        ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.deleteBundleDealItem(saleAccountAndBusinessResponse, shopeeDeleteBundleDealtemV2);

        String message = shopeeResponse.getMessage();
        String error = shopeeResponse.getError();
        if (StringUtils.isNotBlank(error)) {
            return error + ":" + message;
        } else {
            shopeeBundleDealProductListingService.deleteByPrimaryKey(List.of(shopeeBundleDealProductListing.getId()));
            return null;
        }
    }

    public List<List<String>> getLastCreatedComposeSpu() {
        LocalDateTime lastCreatedDateTime = getLastCreatedDateTime();
        if (lastCreatedDateTime == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ShopeeBundleDealSpuCompose> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(ShopeeBundleDealSpuCompose::getCreatedTime, lastCreatedDateTime.toLocalDate());
        List<ShopeeBundleDealSpuCompose> shopeeBundleDealSpuComposes = shopeeBundleDealSpuComposeService.list(queryWrapper);
        if (CollectionUtils.isEmpty(shopeeBundleDealSpuComposes)) {
            return Collections.emptyList();
        }
        return shopeeBundleDealSpuComposes.stream()
                .map(ShopeeBundleDealSpuCompose::getSpuCompose)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank).map(spuStr -> {
                    String[] spuArray = spuStr.split(",");
                    return Arrays.asList(spuArray);
                }).collect(Collectors.toList());
    }

    private LocalDateTime getLastCreatedDateTime() {
        LambdaQueryWrapper<ShopeeBundleDealSpuCompose> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ShopeeBundleDealSpuCompose::getCreatedTime);
        wrapper.last("limit 1");
        ShopeeBundleDealSpuCompose spuCompose = shopeeBundleDealSpuComposeService.getOne(wrapper);
        if (spuCompose == null) {
            return null;

        }
        return spuCompose.getCreatedTime();
    }

    /**
     * 获取最大SPU组合
     *
     * @return
     */
    public List<List<String>> getConnectedSpuComponents() {
        // 1. 获取大数据统计出单的最大SPU组合，`ads_publish_shopee_spu_compose`
        List<AdsPublishShopeeSpuCompose> adsPublishShopeeSpuComposes = adsPublishShopeeSpuComposeService.list();
        if (CollectionUtils.isEmpty(adsPublishShopeeSpuComposes)) {
            XxlJobLogger.log("未获取到大数据最大SPU组合数据集");
            return null;
        }

        List<List<String>> bigDataSpuComposeList = adsPublishShopeeSpuComposes.stream()
                .filter(compose -> StringUtils.isNotBlank(compose.getSpuStr()))
                .map(compose -> {
                    String[] spuArray = compose.getSpuStr().split(",");
                    return Arrays.asList(spuArray);
                }).collect(Collectors.toList());


        //2. 获取最近一天历史记录 `shopee_bundle_deal_spu_compose`，使用 `ConnectedComponents.java` 进行重新组合
        LocalDateTime lastCreatedDateTime = getLastCreatedDateTime();
        LocalDateTime now = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        List<List<String>> historySpuComposes = getLastCreatedComposeSpu();
        if (CollectionUtils.isEmpty(historySpuComposes)) {
            XxlJobLogger.log("无{},历史记录", lastCreatedDateTime);
            saveComposeSpuList(bigDataSpuComposeList);
            return bigDataSpuComposeList;
        }

        bigDataSpuComposeList.addAll(historySpuComposes);
        // 重新组合
        List<List<String>> connectedComponents = ConnectedComponents.findConnectedComponents(ConnectedComponents.buildGraph(bigDataSpuComposeList));
        if (!lastCreatedDateTime.toLocalDate().equals(now.toLocalDate())) {
            XxlJobLogger.log("last:{}, 保存SPU组合数据", lastCreatedDateTime);
            saveComposeSpuList(connectedComponents);
        }
        return connectedComponents;
    }

    private void saveComposeSpuList(List<List<String>> composeSpuList) {
        try {
            XxlJobLogger.log("开始保存SPU组合数据，共{}条", composeSpuList.size());
            List<ShopeeBundleDealSpuCompose> spuComposes = composeSpuList.stream().map(spuCompose -> {
                ShopeeBundleDealSpuCompose shopeeBundleDealSpuCompose = new ShopeeBundleDealSpuCompose();
                shopeeBundleDealSpuCompose.setSpuCompose(StringUtils.join(spuCompose, ","));
                shopeeBundleDealSpuCompose.setCreatedTime(LocalDateTime.now());
                shopeeBundleDealSpuCompose.setUpdatedTime(LocalDateTime.now());
                return shopeeBundleDealSpuCompose;
            }).collect(Collectors.toList());

            shopeeBundleDealSpuComposeService.saveBatch(spuComposes, 300);
            XxlJobLogger.log("SPU组合数据保存成功，共保存{}条记录", spuComposes.size());
        } catch (Exception e) {
            XxlJobLogger.log("保存SPU组合数据失败：{}", e.getMessage());
            throw e;
        }
    }


    public ShopeeMarketingBundleDeal createComposeBundelDeal(ShopeeMarketingConfig config, List<String> composeSpu, SaleAccountAndBusinessResponse saleAccount, String composeBundelDealName) {
        // 记录处理报告
        Timestamp nowTime = new Timestamp(new Date().getTime());
        String accountNumber = saleAccount.getAccountNumber();
        FeedTask insertFeedTask = getComposeInsertFeedTask(config, saleAccount.getAccountNumber());
        try {
            Timestamp startTime = Timestamp.valueOf(insertFeedTask.getCreateTime().toLocalDateTime().plusHours(2));
            // 构建请求参数
            ShopeeMarketingBundleDeal.ShopeeMarketingBundleDealBuilder bundleDealBuilder = ShopeeMarketingBundleDeal.builder();
            BundleDealConfigParam bundleDealConfigParam = JSON.parseObject(config.getRuleJson(), BundleDealConfigParam.class);
            ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.addComposeBundelDeal(composeBundelDealName, saleAccount, bundleDealConfigParam, startTime, bundleDealBuilder, nowTime);

            if (ObjectUtils.isNotEmpty(shopeeResponse) && (StringUtils.isBlank(shopeeResponse.getError()) || "-".equals(shopeeResponse.getError())) && StringUtils.isNotBlank(shopeeResponse.getResponse())) {
                String bundleDealId = JSON.parseObject(shopeeResponse.getResponse()).get("bundle_deal_id").toString();

                ShopeeAccountConfig accountConfigByAccount = shopeeAccountConfigService.getAccountConfigByAccount(accountNumber);
                if (ObjectUtils.isNotEmpty(accountConfigByAccount)) {
                    bundleDealBuilder.site(accountConfigByAccount.getSite());
                }

                // 保存优惠套装数据
                ShopeeMarketingBundleDeal shopeeMarketingBundleDeal = bundleDealBuilder
                        .accountNumber(accountNumber)
                        .configId(Long.valueOf(config.getId()))
                        .bundleDealId(Long.valueOf(bundleDealId))
                        .configId(Long.valueOf(config.getId()))
                        .ruleJson(config.getRuleJson())
                        .suitType(ShopeeMarketingBundleDealSuitTypeEnum.COMPOSE.getCode())
                        .spuCompose(StringUtils.join(composeSpu, ","))
                        .status(ShopeeMarketingBundleDealStatusEnum.NEXT.getCode())
                        .addStatus(ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_PENDING.getCode())
                        .productNumber(0)
                        .build();
                shopeeMarketingBundleDealService.insert(shopeeMarketingBundleDeal);

                // 记录处理报告
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "创建成功，活动ID：" + shopeeMarketingBundleDeal.getBundleDealId() + "，套装名称：" + shopeeMarketingBundleDeal.getName());
                return shopeeMarketingBundleDeal;
            } else {
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "创建优惠套装失败，shopee平台接口报错:" + JSON.toJSONString(shopeeResponse));
            }
        } catch (Exception e) {
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "创建优惠套装失败，shopee平台接口报错:" + e.getMessage());
        }
        return null;
    }

    /**
     * 创建FeedTask
     *
     * @param config
     * @param account
     * @return
     */
    private static FeedTask getComposeInsertFeedTask(ShopeeMarketingConfig config, String account) {
        FeedTask insertFeedTask = ShopeeFeedTaskHandleUtil.insertFeedTask(feedTask -> {
            feedTask.setAttribute1(config.getName());
            feedTask.setAccountNumber(account);
            feedTask.setTaskType(ShopeeFeedTaskEnum.CREATE_COMPOSE_BUNDLE_DEAL.getValue());
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTask.setRunTime(new Timestamp(new Date().getTime()));
            feedTask.setFinishTime(new Timestamp(new Date().getTime()));
            feedTask.setCreatedBy("admin");
        });
        return insertFeedTask;
    }


    public void submitItem2ComposeBundelDeal(List<EsShopeeItem> esShopeeItemList, ShopeeMarketingBundleDeal composeBundelDeal, SaleAccountAndBusinessResponse saleAccount, ShopeeMarketingConfig config) {
        String accountNumber = saleAccount.getAccountNumber();
        String configName = config.getName();
        // 组装商品数据添加当前套装活动中
        List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = esShopeeItemList.stream()
                .limit(2000 - composeBundelDeal.getProductNumber())
                .map(item -> {
                    ShopeeBundleDealProductListing shopeeBundleDealProductListing = new ShopeeBundleDealProductListing();
                    shopeeBundleDealProductListing.setAccountNumber(accountNumber);
                    shopeeBundleDealProductListing.setItemId(Long.parseLong(item.getItemId()));
                    shopeeBundleDealProductListing.setBundleDealId(composeBundelDeal.getBundleDealId());
                    shopeeBundleDealProductListing.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    shopeeBundleDealProductListing.setStatus(1);
                    return shopeeBundleDealProductListing;
                })
                .collect(Collectors.toList());

        log.info("{}，对活动:{}，添加商品数量:{}个", accountNumber, composeBundelDeal.getBundleDealId(), shopeeBundleDealProductListings.size());
        ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.addBundelDealItem(saleAccount, shopeeBundleDealProductListings, composeBundelDeal);
        log.info("response: \n{}", JSON.toJSONString(shopeeResponse));
        // 调用接口失败
        if (ObjectUtils.isEmpty(shopeeResponse) || StringUtils.isNotBlank(shopeeResponse.getError())) {
            ShopeeFeedTaskHandleUtil.insertFeedTask(feedTask -> {
                feedTask.setAttribute1(configName);
                feedTask.setAccountNumber(accountNumber);
                feedTask.setAssociationId(String.valueOf(composeBundelDeal.getBundleDealId()));
                feedTask.setTaskType(ShopeeFeedTaskEnum.ADD_COMPOSE_BUNDLE_DEAL_ITEM.getValue());
                feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                feedTask.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
                feedTask.setRunTime(new Timestamp(new Date().getTime()));
                feedTask.setFinishTime(new Timestamp(new Date().getTime()));
                feedTask.setResultMsg(String.format("店铺:%s，套装名称：%s，添加商品任务，执行失败，原因\n %s", accountNumber, configName, JSON.toJSONString(shopeeResponse)));
                feedTask.setCreatedBy("admin");
            });
        }

        // 部分数据添加失败
        JSONObject responseJSON = JSON.parseObject(shopeeResponse.getResponse());
        if (responseJSON == null) {
            return;
        }
        if (responseJSON.containsKey("failed_list") && CollectionUtils.isNotEmpty(responseJSON.getJSONArray("failed_list"))) {
            List<JSONObject> failedList = JSON.parseObject(responseJSON.getString("failed_list"), new TypeReference<List<JSONObject>>() {
            });
            failedList.forEach(obj -> {
                // 删除已经存在得item数据
                esShopeeItemList.removeIf(item -> item.getItemId().equals(obj.getString("item_id")));

                // 记录失败的商品数据
                saveFeedTask(accountNumber, composeBundelDeal, obj.getString("item_id"), config, ResultStatusEnum.RESULT_FAIL.getStatusCode(), obj.getString("fail_message"));
            });
        }

        // 添加成功
        if (responseJSON.containsKey("success_list") && org.apache.commons.collections.CollectionUtils.isNotEmpty(responseJSON.getJSONArray("success_list"))) {
            // 解析成功的商品id
            List<String> successList = JSON.parseObject(responseJSON.getString("success_list"), new TypeReference<List<String>>() {
            });

            esShopeeItemList.removeIf(item -> successList.contains(item.getItemId()));

            // 只添加添加成功的商品数据
            List<ShopeeBundleDealProductListing> successBundleDealProductListing = shopeeBundleDealProductListings.stream().filter(t -> successList.contains(Long.toString(t.getItemId()))).collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(successBundleDealProductListing)) {
                //批量添加套装商品表数据
                shopeeBundleDealProductListingService.batchInsert(successBundleDealProductListing);

                //更新shopee_marketing_bundle_deal
                int totalProductNumber = composeBundelDeal.getProductNumber() + successBundleDealProductListing.size();
                if (totalProductNumber >= 2000) {
                    composeBundelDeal.setAddStatus(ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_FULL.getCode());
                } else {
                    composeBundelDeal.setAddStatus(ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_NOT_FULL.getCode());
                }
                composeBundelDeal.setProductNumber(totalProductNumber);
                composeBundelDeal.setLastSyncProductExecTime(new Timestamp(new Date().getTime()));
                composeBundelDeal.setUpdatedTime(new Timestamp(new Date().getTime()));
                shopeeMarketingBundleDealService.updateByPrimaryKeySelective(composeBundelDeal);

                // 处理报告
                successBundleDealProductListing.forEach(t -> {
                    saveFeedTask(accountNumber, composeBundelDeal, String.valueOf(t.getItemId()), config, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "添加成功");
                });

            }
        }
    }

    /**
     * 处理报告
     *
     * @param account
     * @param shopeeMarketingBundleDeal
     * @param itemId
     * @param statusCode
     */
    private static void saveFeedTask(String account, ShopeeMarketingBundleDeal shopeeMarketingBundleDeal, String itemId, ShopeeMarketingConfig shopeeMarketingConfig, int statusCode, String message) {
        ShopeeFeedTaskHandleUtil.insertFeedTask(feedTask -> {
            feedTask.setAttribute1(Objects.isNull(shopeeMarketingConfig) ? null : shopeeMarketingConfig.getName());
            feedTask.setAccountNumber(account);
            feedTask.setArticleNumber(itemId);
            feedTask.setAssociationId(String.valueOf(shopeeMarketingBundleDeal.getBundleDealId()));
            feedTask.setTaskType(ShopeeFeedTaskEnum.ADD_COMPOSE_BUNDLE_DEAL_ITEM.getValue());
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            feedTask.setResultStatus(statusCode);
            feedTask.setRunTime(new Timestamp(new Date().getTime()));
            feedTask.setFinishTime(new Timestamp(new Date().getTime()));
            feedTask.setResultMsg(message);
            feedTask.setCreatedBy("admin");
        });
    }
}
