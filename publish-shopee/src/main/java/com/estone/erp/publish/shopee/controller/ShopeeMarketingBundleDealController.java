package com.estone.erp.publish.shopee.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.shopee.component.download.ShopeeDownloadTypeEnums;
import com.estone.erp.publish.shopee.dto.AccountNumberVo2;
import com.estone.erp.publish.shopee.dto.BundleDealProductVO;
import com.estone.erp.publish.shopee.dto.ShopeeMarketingBundleDealVO;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingConfigTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeStopstatusEnum;
import com.estone.erp.publish.shopee.jobHandler.ShopeeBundleDealSyncJobHandler;
import com.estone.erp.publish.shopee.mapper.ShopeeLogisticHandleMapper;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.mq.model.ShopeeStopMarketingActivityDO;
import com.estone.erp.publish.shopee.service.*;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingExample;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealProductListingService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-06-18 18:53:27
 */
@RestController
@RequestMapping("shopeeMarketingBundleDeal")
public class ShopeeMarketingBundleDealController {
    @Resource
    private ShopeeMarketingBundleDealService marketingBundleDealService;


    @Resource
    private ShopeeLogisticHandleService shopeeLogisticHandleService;

    @Autowired
    private  ShopeeBundleDealProductListingService bundleDealProductListingService;

    @Resource
    private PermissionsHelper permissionsHelper;

    @Autowired
    private EsShopeeItemService esShopeeItemService;

    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Autowired
    private ShopeeBundleDealSyncJobHandler shopeeBundleDealSyncJobHandler;

    @Autowired
    private ShopeeLogisticHandleMapper shopeeLogisticHandleMapper;

    @Resource
    private ShopeeAccountGroupService shopeeAccountGroupService;

    @Resource
    private ShopeeMarketingConfigService shopeeMarketingConfigService;

    @Resource
    private RabbitMqSender rabbitMqSender;

    @Resource
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;

    @Resource
    private ExcelDownloadLogService excelDownloadLogService;


    @PostMapping
    public ApiResult<?> postShopeeMarketingBundleDeal(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchShopeeMarketingBundleDeal": // 查询列表
                    CQuery<ShopeeMarketingBundleDealCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopeeMarketingBundleDealCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    ShopeeMarketingBundleDealCriteria search = cquery.getSearch();
                    List<Integer> groupIdList = search.getGroupIdList();
                    if (CollectionUtils.isNotEmpty(groupIdList)) {
                        List<String> accountNumberList = shopeeAccountGroupService.getAccountNumberByGroupId(groupIdList);
                        if (CollectionUtils.isNotEmpty(search.getAccountNumberList())) {
                            accountNumberList.retainAll(search.getAccountNumberList());
                        }
                        search.setAccountNumberList(accountNumberList);
                        if (CollectionUtils.isEmpty(search.getAccountNumberList())) {
                            // 组装结果
                            CQueryResult<ShopeeMarketingBundleDeal> result = new CQueryResult<>();
                            result.setTotal(0);
                            result.setTotalPages(0);
                            result.setRows(new ArrayList<>());
                            return result;
                        }
                    }
                    String likeRuleName = search.getLikeRuleName();
                    if (StringUtils.isNotBlank(likeRuleName)) {
                        List<Integer> list = shopeeMarketingConfigService.getConfigIdByLikeRuleName(likeRuleName, ShopeeMarketingConfigTypeEnum.BUNDLE_DEAL);
                        if (CollectionUtils.isNotEmpty(list)) {
                            search.setMarketingIdList(list);
                        } else {
                            // 组装结果
                            CQueryResult<ShopeeMarketingBundleDeal> result = new CQueryResult<>();
                            result.setTotal(0);
                            result.setTotalPages(0);
                            result.setRows(new ArrayList<>());
                            return result;
                        }
                    }
                    isAuth(cquery);
                    CQueryResult<ShopeeMarketingBundleDealVO> results = marketingBundleDealService.search(cquery);
                    return results;
                }
        }
        return ApiResult.newSuccess();
    }

    @PostMapping("download")
    public ApiResult<?> download(@RequestBody(required = true) ApiRequestParam<String> requestParam, HttpServletResponse response) throws IOException {
        int downloadLimitSize = 500000;
        CQuery<ShopeeMarketingBundleDealCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopeeMarketingBundleDealCriteria>>() {});
        ShopeeMarketingBundleDealCriteria search = cquery.getSearch();
        List<Integer> groupIdList = search.getGroupIdList();
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            List<String> accountNumberList = shopeeAccountGroupService.getAccountNumberByGroupId(groupIdList);
            if (CollectionUtils.isNotEmpty(search.getAccountNumberList())) {
                accountNumberList.retainAll(search.getAccountNumberList());
            }
            search.setAccountNumberList(accountNumberList);
            if (CollectionUtils.isEmpty(search.getAccountNumberList())) {
                return ApiResult.newError("查询数据为空");
            }
        }
        String likeRuleName = search.getLikeRuleName();
        if (StringUtils.isNotBlank(likeRuleName)) {
            List<Integer> list = shopeeMarketingConfigService.getConfigIdByLikeRuleName(likeRuleName, ShopeeMarketingConfigTypeEnum.BUNDLE_DEAL);
            if (CollectionUtils.isNotEmpty(list)) {
                search.setMarketingIdList(list);
            } else {
                return ApiResult.newError("查询数据为空");
            }
        }
        isAuth(cquery);
        ShopeeMarketingBundleDealCriteria query = cquery.getSearch();
        ShopeeMarketingBundleDealExample example = query.getExample();
        int count = marketingBundleDealService.countByExample(example);

        if (count == 0) {
            return ApiResult.newError("查询数据为空");
        }

        if (count > downloadLimitSize) {
            return ApiResult.newError("单次导出数据不能超过" + downloadLimitSize + "条");
        }

        // 构造导出日志
        ExcelDownloadLog downloadLog = new ExcelDownloadLog();
        downloadLog.setType(ShopeeDownloadTypeEnums.BUNDLE_DEAL_RECORD.getType());
        downloadLog.setQueryCondition(JSON.toJSONString(search));
        downloadLog.setDownloadCount(count);
        downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
        downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
        // 发送队列
        excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_SHOPEE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_DOWNLOAD_QUEUE_KEY);
        return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
    }


    @GetMapping(value = "/{id}")
    public ApiResult<?> getShopeeMarketingBundleDeal(@PathVariable(value = "id", required = true) Long id) {
        List<BundleDealProductVO> bundleDealProductVoList;
        ShopeeMarketingBundleDeal shopeeMarketingBundleDeal = marketingBundleDealService.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(shopeeMarketingBundleDeal)){
            throw new BusinessException("数据不存在");
        }


        ShopeeAccountConfigExample shopeeAccountConfigExample = new ShopeeAccountConfigExample();
        shopeeAccountConfigExample.createCriteria().andAccountEqualTo(shopeeMarketingBundleDeal.getAccountNumber());
        List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectByExample(shopeeAccountConfigExample);

        Map<String, SalesmanAccountDetail> salesmanAccountDetailMapByEsMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(Lists.newArrayList(shopeeMarketingBundleDeal.getAccountNumber()), SaleChannel.CHANNEL_SHOPEE);

        if (CollectionUtils.isNotEmpty(shopeeAccountConfigs)) {
            shopeeMarketingBundleDeal.setMerchantId(shopeeAccountConfigs.get(0).getMerchantId());
            shopeeMarketingBundleDeal.setMerchantName(shopeeAccountConfigs.get(0).getMerchant());
            shopeeMarketingBundleDeal.setSite(shopeeAccountConfigs.get(0).getSite());
        }

        SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMapByEsMap.get(shopeeMarketingBundleDeal.getAccountNumber());
        if (salesmanAccountDetail != null) {
            shopeeMarketingBundleDeal.setSalesman(CollectionUtils.isNotEmpty(salesmanAccountDetail.getSalesmanSet()) ? StringUtils.join(salesmanAccountDetail.getSalesmanSet(), ",") : "");
            shopeeMarketingBundleDeal.setSalesTeamLeader(salesmanAccountDetail.getSalesTeamLeaderName());
            shopeeMarketingBundleDeal.setSalesSupervisorName(salesmanAccountDetail.getSalesSupervisorName());
        }

        return ApiResult.newSuccess(shopeeMarketingBundleDeal);
    }

    @GetMapping("/getItemList/{id}")
    public ApiResult<?> getItemList(@PathVariable("id") Long id) {
        ShopeeMarketingBundleDeal shopeeMarketingBundleDeal = marketingBundleDealService.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(shopeeMarketingBundleDeal)) {
            throw new BusinessException("数据不存在");
        }

        /*已经添加的商品*/
        ShopeeBundleDealProductListingExample bundleDealProductListingExample = new ShopeeBundleDealProductListingExample();
        ShopeeBundleDealProductListingExample.Criteria criteria = bundleDealProductListingExample.createCriteria();
        criteria.andBundleDealIdEqualTo(shopeeMarketingBundleDeal.getBundleDealId());
        criteria.andAccountNumberEqualTo(shopeeMarketingBundleDeal.getAccountNumber());
        List<ShopeeBundleDealProductListing> bundleDealProductListings = bundleDealProductListingService.selectByExample(bundleDealProductListingExample);
        if (CollectionUtils.isEmpty(bundleDealProductListings)) {
            return ApiResult.newError("数据不存在");
        }

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(shopeeMarketingBundleDeal.getAccountNumber());
        request.setItemIdList(bundleDealProductListings.stream().map(t -> t.getItemId().toString()).collect(Collectors.toList()));
        request.setQueryFields(new String[]{"itemId", "name", "price", "stock", "site"});
        request.setIsGoods(true);
        List<EsShopeeItem> esShopeeItems = esShopeeItemService.getEsShopeeItems(request);
        if (CollectionUtils.isEmpty(esShopeeItems)) {
            return ApiResult.newError("数据不存在");
        }
        Map<String, List<EsShopeeItem>> esShopeeItemMap = esShopeeItems.stream().collect(Collectors.groupingBy(EsShopeeItem::getItemId));
        List<BundleDealProductVO> existBundleDealProductVOS = setInfo(bundleDealProductListings, shopeeMarketingBundleDeal, esShopeeItemMap, Boolean.TRUE);
        return ApiResult.newSuccess(existBundleDealProductVOS);
    }

    /**
     * 设置商品信息
     *
     * @param shopeeMarketingBundleDeal
     * @param finalEsShopeeItemMap
     * @return void
     * <AUTHOR>
     * @date 2024/7/3 15:20
     */
    private List<BundleDealProductVO> setInfo(List<ShopeeBundleDealProductListing> bundleDealProductListings, ShopeeMarketingBundleDeal shopeeMarketingBundleDeal, Map<String, List<EsShopeeItem>> finalEsShopeeItemMap, Boolean isSignUp) {
        // 通过店铺获取物流渠道
        Map<String, String> siteLogisticMap = shopeeLogisticHandleService.selectLogisticName(shopeeMarketingBundleDeal.getAccountNumber());

        // 设置商品信息
        List<BundleDealProductVO> bundleDealProductVOS = bundleDealProductListings.stream().map(product -> {
            String itemId = String.valueOf(product.getItemId());

            BundleDealProductVO bundleDealProductVO = new BundleDealProductVO();
            if (MapUtils.isNotEmpty(finalEsShopeeItemMap)) {
                List<EsShopeeItem> esShopeeItems = finalEsShopeeItemMap.get(itemId);

                if (CollectionUtils.isNotEmpty(esShopeeItems)) {
                    List<EsShopeeItem> sortedEsShopeeItems = esShopeeItems.stream().sorted(Comparator.comparing(EsShopeeItem::getPrice)).collect(Collectors.toList());


                    BeanUtils.copyProperties(sortedEsShopeeItems.get(0), bundleDealProductVO);
                    if (StringUtils.isNotBlank(bundleDealProductVO.getSite())) {
                        String logisticCode = siteLogisticMap.get(bundleDealProductVO.getSite());
                        if (StringUtils.isNotBlank(logisticCode)) {
                            bundleDealProductVO.setLogistics(logisticCode);
                        }
                    }
                    /*价格设置为区间*/
                    if (sortedEsShopeeItems.size() == 1) {
                        bundleDealProductVO.setPrice(sortedEsShopeeItems.get(0).getPrice().toString());
                    } else {
                        bundleDealProductVO.setPrice(sortedEsShopeeItems.get(0).getPrice().toString() + "-" + sortedEsShopeeItems.get(sortedEsShopeeItems.size() - 1).getPrice().toString());
                    }
                }
                bundleDealProductVO.setItemId(itemId);
                bundleDealProductVO.setStatus(product.getStatus());
            }
            bundleDealProductVO.setIsSignUp(isSignUp);
            return bundleDealProductVO;
        }).collect(Collectors.toList());
        return bundleDealProductVOS;
    }


    @PostMapping("sync")
    public ApiResult<?> sync(@RequestBody @Valid AccountNumberVo2 accountNumberVo2) throws IOException {
        String[] split = accountNumberVo2.getAccounts().split(",");
        String userName = WebUtils.getUserName();
        for (String account : split) {
            ShopeeExecutors.bundleDealSyncJob(() -> {
                DataContextHolder.setUsername(userName);
                shopeeBundleDealSyncJobHandler.updateBundleDeal(account);
            });
        }
        return ApiResult.newSuccess("正在同步，请前往处理报告查看结果");
    }


    private void isAuth(CQuery<ShopeeMarketingBundleDealCriteria> cquery) {
        ShopeeMarketingBundleDealCriteria search = cquery.getSearch();
        List<String> saleList = search.getSaleManList();
        List<String> saleLeaderList = search.getSaleTeamLeaderList();
        List<String> saleManagerList = search.getSalesSupervisorList();

        List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(search.getAccountNumberList(), saleManagerList, saleLeaderList, saleList, SaleChannel.CHANNEL_SHOPEE, true);
        search.setAccountNumberList(currentUserPermission);
    }


    @PostMapping("/end")
    public ApiResult<?> endBundelDealItem(@RequestBody List<ShopeeMarketingBundleDeal> shopeeMarketingBundleDeals) {
        String userName = WebUtils.getUserName();
        for (ShopeeMarketingBundleDeal shopeeMarketingBundleDeal : shopeeMarketingBundleDeals) {
            Integer stopStatus = shopeeMarketingBundleDeal.getStopStatus();
            if(stopStatus != null && (stopStatus == ShopeeStopstatusEnum.WAIT.getCode() || stopStatus == ShopeeStopstatusEnum.SUCCESS.getCode()) ){
                continue;
            }
            // 创建处理报告信息
            FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask((task) -> {
                task.setTaskType(ShopeeFeedTaskEnum.STOP_BUNDLE_DEAL.getValue());
                task.setAccountNumber(shopeeMarketingBundleDeal.getAccountNumber());
                task.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
                task.setResultMsg("套装名称：" + shopeeMarketingBundleDeal.getName() + "停止中");
                task.setAssociationId(String.valueOf(shopeeMarketingBundleDeal.getId()));
                task.setAttribute1(shopeeMarketingBundleDeal.getRuleName());
                task.setAttribute5(shopeeMarketingBundleDeal.getConfigId() != null ? shopeeMarketingBundleDeal.getConfigId().toString() : "");
                task.setCreatedBy(StringUtils.isNotBlank(userName) ? userName : StrConstant.ADMIN);
            });
            //更新停止状态
            ShopeeMarketingBundleDeal marketingBundleDeal = new ShopeeMarketingBundleDeal();
            marketingBundleDeal.setStopStatus(ShopeeStopstatusEnum.WAIT.getCode());
            marketingBundleDeal.setId(shopeeMarketingBundleDeal.getId());
            shopeeMarketingBundleDealService.updateByPrimaryKeySelective(marketingBundleDeal);

            ShopeeStopMarketingActivityDO shopeeStopMarketingActivityDO = new ShopeeStopMarketingActivityDO();
            shopeeStopMarketingActivityDO.setAccountNumber(shopeeMarketingBundleDeal.getAccountNumber());
            shopeeStopMarketingActivityDO.setMarketingActivityId(shopeeMarketingBundleDeal.getBundleDealId());
            shopeeStopMarketingActivityDO.setFeedTaskId(feedTask.getId());
            shopeeStopMarketingActivityDO.setName(shopeeMarketingBundleDeal.getName());
            shopeeStopMarketingActivityDO.setType(ShopeeMarketingConfigTypeEnum.BUNDLE_DEAL.getCode());
            shopeeStopMarketingActivityDO.setId(shopeeMarketingBundleDeal.getId().intValue());
            //发送队列
            rabbitMqSender.allPublishVHostRabbitTemplateSend(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_STOP_MARKETING_ACTIVITY_ROUTE_KEY, JSON.toJSON(shopeeStopMarketingActivityDO));
        }
        return ApiResult.newSuccess("正在结束，请前往处理报告查看结果");
    }


    @PostMapping("refreshSuitBundleDeal")
    public ApiResult<String> refreshSuitBundleDeal() {
        int total = shopeeMarketingBundleDealService.refreshSuitBundleDeal();
        return ApiResult.newSuccess("共刷新" + total + "条数据");
    }
}