package com.estone.erp.publish.system.product.bean;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/5/1316:40
 */
@Data
@Slf4j
public class SpuInfo {
    /**
     * 图片
    */
    private String firstImage;
    /**
     * spu
     */
    private String spu;
    /**
     * 标题
     */
    private String title;
    /**
     * 子sku集合
     */
    private List<String> sonSkus = new ArrayList<>();
    /**
     * 子sku状态集合
     */
    private List<String> skuStatus = new ArrayList<>();
    /**
     * 分类id
     */
    private Integer categoryId;
    /**
     * 分类code全路径
     */
    private String fullpathcode;
    /**
     * 分类名拼接
     */
    private String categoryPath;
    /**
     * 是否有品牌 0,没有，1,是   2，否
     */
    private Integer isBrand;
    /**
     * 销售成本价
     */
    private BigDecimal saleCost;
    /**
     * 预估包裹重量
     */
    private BigDecimal packageWeight;
    /**
     * 产品标签
     */
    private String tag;
    /**
     * 产品标签code
     */
    private String tagCode;
    /**
     * 上贴备注
     */
    private String postRemark;
    /**
     * 禁售平台集合
     */
    private List<String> salesProbition = new ArrayList<>();
    /**
     * 搭配包材
     */
    private String matchMaterials;
    /**
     * 包材
     */
    private String packingMaterials;
    /**
     * 第一个正常的录入时间
     */
    private Date createAt;
    /**
     * 净重
     */
    private Double productWeight;
    /**
     * 参考链接1
     */
    private String egLink1;
    /**
     * 参考链接2
     */
    private String egLink2;
    /**
     * 参考链接3
     */
    private String egLink3;
    /**
     * sku描述
     */
    private String descEn;

    /**
     * 短描述
     */
    private String shortDescription;

    /**
     * 产品类型 0：单属性， 1：多属性
     */
    private Integer type;
    /**
     * sku 标题  数组结构
     */
    private String descTitle;

    /**
     * 描述关键词
     */
    private String mustKeyword;


    private BigDecimal length;
    /**
     * 宽
     */
    private BigDecimal wide;
    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 标题组成元素
     * {"品牌":["",""],"类目词":["",""],"大词":["",""],"产品属性词":["",""],"长尾词":["",""],"修饰词":["",""]}
     */
    private String composeElements;

    /**
     * 刊登关键词
     */
    private String publishKeyWord;
    /**
     * 刊登标题
     */
    private String publishTitle;
    /**
     * 刊登短描述
     */
    private List<SpuShortDesc> publishShortDes;

    /**
     * 主图数组
     */
    private String firstImageAdd;

    /**
     * 文案
     */
    private String translateEmpName;

    /**
     * 产品开发
     */
    private String devEmpName;

    /**
     * 商标词信息 json集合
     */
   // private String trademark;
    /**
     * 刊登重量
     */
    private Double publishWeight;
    public String getFirstImage() {
        if (null != firstImageAdd) {
            try {
                List<String> firstImageList = JSON.parseArray(this.firstImageAdd, String.class);
                if (StringUtils.isNotEmpty(firstImage)){
                    firstImageList.add(firstImage);
                }
                Collections.shuffle(firstImageList);
                return firstImageList.get(0);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return firstImage;
    }

    /*public List<TrademarkVO> getTrademarkVOs() {
        if(StringUtils.isBlank(this.getTrademark())) {
            return Collections.emptyList();
        }
        try {
            List<TrademarkVO> trademarkVOs = JSONArray.parseArray(this.getTrademark(), TrademarkVO.class);
            return trademarkVOs;
        }catch (Exception e) {
            log.error("spu" + this.getSpu() + "商标词转换异常" + e.getMessage(), e);
        }

        return Collections.emptyList();
    }*/
}
