package com.estone.erp.publish.walmart.service;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.elasticsearch.model.EsTemuItemSkuInfo;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.walmart.model.WalmartCategoryAttribute;
import com.estone.erp.publish.walmart.model.WalmartCategoryAttributeCriteria;
import com.estone.erp.publish.walmart.model.WalmartCategoryAttributeExample;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> walmart_category_attribute
 * 2022-08-05 10:52:34
 */
public interface WalmartCategoryAttributeService {
    int countByExample(WalmartCategoryAttributeExample example);

    CQueryResult<WalmartCategoryAttribute> search(CQuery<WalmartCategoryAttributeCriteria> cquery);

    List<WalmartCategoryAttribute> selectByExample(WalmartCategoryAttributeExample example);

    WalmartCategoryAttribute selectByPrimaryKey(Integer id);

    WalmartCategoryAttribute selectBySubCategoryId(String subCategoryId);

    int insert(WalmartCategoryAttribute record);

    int updateByPrimaryKeySelective(WalmartCategoryAttribute record);

    int updateByExampleSelective(WalmartCategoryAttribute record, WalmartCategoryAttributeExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    void refreshAll(HashMap<String, JSONObject> visiblePropertiesMap);

    /**
     * 根据产品属性匹配平台属性
     * @param categoryId
     * @param productInfoList
     * @return
     */
    Map<String, Map<String, Object>> matchPlatformAttribute(String categoryId, List<ProductInfo> productInfoList);

    /**
     * 根据temu属性匹配平台属性
     * @param defaultCategoryId
     * @param skuInfoList
     * @return
     */
    Map<String,Map<String,Object>> matchTemuPlatformAttribute(String defaultCategoryId, List<EsTemuItemSkuInfo> skuInfoList);

    Map<String, Map<String, Object>> matchDefaultColorAttribute(List<ProductInfo> productInfos);

    /**
     * 根据产品属性匹配新平台属性
     * @param subCategoryName
     * @param productInfos
     * @return
     */
    Map<String, Map<String, Object>> matchPlatformNewAttribute(String subCategoryName, List<ProductInfo> productInfos);
}