package com.estone.erp.publish.ozon.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItemOffline;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateDO;
import com.estone.erp.publish.ozon.model.vo.OzonActionItemVo;
import com.estone.erp.publish.ozon.model.vo.OzonFailReportVO;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskCriteria;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-09 17:30
 */
public interface OzonFeedTaskService {

    FeedTask newMarketingTask(boolean success, String msg, Long businessId, String accountNumber, String taskType, String marketingName, Integer marketingId, Integer marketingTaskId, OzonActionItemVo ozonActionItemVo);

    FeedTask createTask(boolean success, String msg, Long businessId, String accountNumber, String taskType, OzonActionItemVo ozonActionItemVo, Timestamp createTime, Timestamp finishTime);

    FeedTask newTask(Long productId, String accountNumber, String taskType, String sku, String ...s);
    FeedTask initPublishFeedTask(Integer excelId, Integer rowIndexId, String ruleName, Long productId, String accountNumber, String taskType, String sku, String ...s);

    FeedTask newWaitingTask(Long productId, String accountNumber, String taskType, String sku, String... s);

    void succeedTask(FeedTask feedTask, String msg, String ...s);

    void runningTask(FeedTask feedTask, String msg);

    void failTask(FeedTask feedTask, String msg, String ...s);

    void batchNewUpdateFeeds(List<OzonUpdateDO> ozonUpdateDOS,String taskType);

    void batchInsertSelective(List<FeedTask> feedTasks);

    void batchUpdateFail(String accountNumber, List<OzonUpdateDO> ozonUpdateDOS, String taskType, String errorMsg);

    List<FeedTask> selectByExample(FeedTaskExample feedTaskExample, String name);

    void batchUpdateFeeds(List<FeedTask> feedTasks);

    /**
     * 批量将任务改成运行中的状态
     * @param feedTasks
     */
    void batchUpdateTaskIdAndRunning(List<FeedTask> feedTasks);

    void batchNewItemFeeds(List<EsOzonItem> itemList, String taskType);
    void batchOfflineItemFeeds(List<EsOzonItemOffline> itemList, String taskType);

    void newPublishFailFeedTask(String templateId, String accountNumber, String articleNumber, String message, String sellerSku, String ruleName, Integer excelId, Integer rowIndex);

    void updatePublishFail(Long feedTaskId, Integer templateId, String accountnumber, String message);

    void updateFail(Long feedTaskId, String message, String taskType);
    void updateSuccess(Long feedTaskId, String message);

    ApiResult<List<OzonFailReportVO>> getPublishFailReport(String templateId);

    FeedTask getExecutingSyncItemTask(String accountNumber, List<String> syncTypeList);

    void addFailFeedTask(Long productId, String accountNumber, String taskType, String sku, String failMsg, String ...s);

    void updateTaskStatus(FeedTask feedTask, Integer status);

    void updateUpdateItemFailTask(String accountNumber, Long taskId, String message, String taskType);

    /**
     * 判断规定时间内是否存在正在执行的任务
     * @param accountNumber
     * @param taskType
     * @param day 近几天，填负数，填null则不限制时间
     * @return
     */
    boolean existExecutingTask(String accountNumber, String taskType, Integer day);

    /**
     * 导出Feed Task数据
     * @param queryCondition 查询条件
     * @return 下载日志ID
     */
    Integer exportFeedTasks(CQuery<FeedTaskCriteria> queryCondition);

    /**
     * 导出刊登的feedTask 数据
     * @param queryCondition
     * @return
     */
    Integer exportPublishFeedTasks(CQuery<FeedTaskCriteria> queryCondition);

    void insert(FeedTask feedTask);

    /**
     * 更新task 为完成状态
     * @param feedTasks
     * @param taskStatus
     * @param resultStatus
     * @param msg
     * @return
     */

    int batchUpdateFeedTaskToFinish(List<FeedTask> feedTasks, int taskStatus, int resultStatus, String msg);


    FeedTask selectById(Long id);
}
