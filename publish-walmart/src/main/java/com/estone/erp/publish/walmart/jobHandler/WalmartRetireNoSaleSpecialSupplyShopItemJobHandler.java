package com.estone.erp.publish.walmart.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.WalmartExecutors;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.order.OrderUtils;
import com.estone.erp.publish.system.order.modle.WalmartSaleQuantity;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.bean.ForbiddenAndSpecical;
import com.estone.erp.publish.walmart.enums.ItemLifecycleStatusEnum;
import com.estone.erp.publish.walmart.enums.WalmartFeedTaskMsgEnum;
import com.estone.erp.publish.walmart.model.WalmartAccountConfig;
import com.estone.erp.publish.walmart.model.WalmartAccountConfigExample;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartItemExample;
import com.estone.erp.publish.walmart.service.WalmartAccountConfigService;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.estone.erp.publish.walmart.util.WalmartItemUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * 特供产品限时下架
 */
@Slf4j
@Component
public class WalmartRetireNoSaleSpecialSupplyShopItemJobHandler extends AbstractJobHandler {

    @Resource
    private WalmartItemService walmartItemService;

    @Resource
    private SaleAccountService saleAccountService;

    @Resource
    private WalmartAccountConfigService walmartAccountConfigService;

    public WalmartRetireNoSaleSpecialSupplyShopItemJobHandler() {
        super("WalmartRetireNoSaleSpecialSupplyShopItemJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam {
        /**
         * 店铺
         */
        private List<String> accountNumberList;

        /**
         * 平台主管
         */
        private String platformSupervisor;

        /**
         * SKU
         */
        private List<String> sellerSkuList;
    }

    @Override
    @XxlJob("WalmartRetireNoSaleSpecialSupplyShopItemJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("开始执行特供产品限时下架定时任务");
        // 解析参数
        InnerParam innerParam = parseParam(param);
        if (innerParam == null) {
            innerParam = new InnerParam();
        }

        List<String> managedAccountNumbers = saleAccountService.getEmployeeManagedAccountNumbers(List.of(innerParam.getPlatformSupervisor()), SaleChannel.CHANNEL_WALMART);


        WalmartAccountConfigExample example = new WalmartAccountConfigExample();
        WalmartAccountConfigExample.Criteria criteria = example.createCriteria();
        criteria.andSpecialSupplyShopEqualTo(true);
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumberList())) {
            criteria.andAccountNumberIn(innerParam.getAccountNumberList());
        }
        // 获取特供店铺
        List<WalmartAccountConfig> specialSupplyShops = walmartAccountConfigService.selectByExample(example);


        if (CollectionUtils.isEmpty(specialSupplyShops)) {
            XxlJobLogger.log("没有找到特供店铺");
            return ReturnT.SUCCESS;
        }

        ApiResult<List<WalmartSaleQuantity>> todaySoldWalmartAsin = OrderUtils.getTodaySoldWalmartAsin();
        if (!todaySoldWalmartAsin.isSuccess()) {
            XxlJobLogger.log("执行结束，获取当天出单量SKU失败");
            return ReturnT.SUCCESS;
        }
        List<String> todaySoldSkuList = todaySoldWalmartAsin.getResult().stream().map(saleQuantity -> {
            String accountNumber = saleQuantity.getAccountNumber();
            String sku = saleQuantity.getSku();
            if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(sku)) {
                return null;
            }
            return accountNumber + "_" + sku;
        }).collect(Collectors.toList());


        // 控制线程池数量，创建Semaphore信号量
        Semaphore sp = new Semaphore(10);
        // 处理特供店铺
        for (WalmartAccountConfig shop : specialSupplyShops) {
            if (!managedAccountNumbers.contains(shop.getAccountNumber())) {
                XxlJobLogger.log("非平台主管店铺,{}", shop.getAccountNumber());
                continue;
            }

            List<String> sellerSkuList = innerParam.getSellerSkuList();
            sp.acquire();
            WalmartExecutors.executeRetireNonSpecialSupplyShopItem(() -> {
                try {
                    processSpecialSupplyShop(shop, sellerSkuList, todaySoldSkuList);
                } catch (Exception e) {
                    XxlJobLogger.log("处理店铺" + shop.getAccountNumber() + "时出错：", e);
                } finally {
                    sp.release();
                }
            });
        }

        XxlJobLogger.log("执行特供产品限时下架定时任务执行完成");
        return ReturnT.SUCCESS;
    }

    private void processSpecialSupplyShop(WalmartAccountConfig shop, List<String> sellerSkuList, List<String> todaySoldSkuList) {
        String accountNumber = shop.getAccountNumber();
        XxlJobLogger.log("处理特供店铺：" + accountNumber);

        // 获取账号信息
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber, true);
        if (account == null || !SaleAccountStastusEnum.NORMAL.getCode().equals(account.getAccountStatus())) {
            XxlJobLogger.log("账号" + accountNumber + "不存在或状态不正常");
            return;
        }

        // 分页查询需要下架的产品
        int limit = 2000;
        Long lastId = 0L;
        while (true) {
            WalmartItemExample example = new WalmartItemExample();
            example.setFiledColumns("id, account_number, seller_sku, main_sku, sku,special_goods_code,special_goods_name,create_date");
            example.setOrderByClause("id ASC");
            example.setOffset(0);
            example.setLimit(limit);
            WalmartItemExample.Criteria criteria = example.createCriteria()
                    .andAccountNumberEqualTo(accountNumber)
                    .andSalesLessThanOrOrderCountIsNull("order_last_30d_count", 0)
                    .andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.ACTIVE.getCode());

            if (lastId > 0) {
                criteria.andIdGreaterThan(lastId);
            }

            if (CollectionUtils.isNotEmpty(sellerSkuList)) {
                criteria.andSellerSkuIn(sellerSkuList);
            }

            List<WalmartItem> items = walmartItemService.selectByExample(example);
            if (CollectionUtils.isEmpty(items)) {
                break;
            }

            lastId = items.get(items.size() - 1).getId();

            List<String> skuList = items.stream().map(WalmartItem::getSku).distinct().collect(Collectors.toList());

            Map<String, ForbiddenAndSpecical> specialGoodsCodeMap = ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSkuBatch(skuList, 250);

            // 获取店铺配置的特供标签code
            List<Integer> specialSupplyCodesList = CommonUtils.splitList(shop.getSpecialSupplyCodes(), ",")
                    .stream().map(s -> Integer.valueOf(s.trim())).collect(Collectors.toList());
            // 30天前
            LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minusDays(30);

            // 过滤出包含特供标签的产品
            List<WalmartItem> itemsToRetire = items.stream()
                    .filter(item -> !todaySoldSkuList.contains(accountNumber + "_" + item.getSellerSku()))
                    .filter(item -> {
                        List<Integer> specialGoodsCodeList = Optional.ofNullable(specialGoodsCodeMap.get(item.getSku())).map(ForbiddenAndSpecical::getSpecialGoodsTypes)
                                .orElse(Collections.emptyList());
                        return WalmartItemUtils.containsSpecialTagCode(specialGoodsCodeList, specialSupplyCodesList);
                    })
                    .filter(item -> {
                        Timestamp createDate = item.getCreateDate();
                        // 上架时间是否在30天前
                        return createDate != null && createDate.before(Timestamp.valueOf(localDateTime));
                    })
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(itemsToRetire)) {
                // 下架产品
                walmartItemService.batchRetire(itemsToRetire, account, StrConstant.ADMIN, WalmartFeedTaskMsgEnum.RETIRE_NO_SALE_SPECIAL_ACCOUNT_PUBLISH_SKU.getMsg());
            }
        }
    }


    /**
     * 解析任务参数
     */
    private InnerParam parseParam(String param) {
        if (StringUtils.isBlank(param)) {
            return null;
        }
        try {
            return JSON.parseObject(param, InnerParam.class);
        } catch (Exception e) {
            XxlJobLogger.log("解析参数异常: {}", e.getMessage());
            return null;
        }
    }
}
