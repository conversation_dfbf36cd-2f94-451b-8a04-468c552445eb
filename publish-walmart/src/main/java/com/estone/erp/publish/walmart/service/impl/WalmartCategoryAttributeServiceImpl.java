package com.estone.erp.publish.walmart.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.elasticsearch.model.EsAttrInfo;
import com.estone.erp.publish.elasticsearch.model.EsTemuItemSkuInfo;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartNewCategoryAttribute;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartNewCategoryAttributeService;
import com.estone.erp.publish.walmart.mapper.WalmartCategoryAttributeMapper;
import com.estone.erp.publish.walmart.model.WalmartCategory;
import com.estone.erp.publish.walmart.model.WalmartCategoryAttribute;
import com.estone.erp.publish.walmart.model.WalmartCategoryAttributeCriteria;
import com.estone.erp.publish.walmart.model.WalmartCategoryAttributeExample;
import com.estone.erp.publish.walmart.service.WalmartCategoryAttributeService;
import com.estone.erp.publish.walmart.service.WalmartCategoryService;
import com.estone.erp.publish.walmart.util.WalmartCategoryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR> walmart_category_attribute
 * 2022-08-05 10:52:34
 */
@Service("walmartCategoryAttributeService")
@Slf4j
public class WalmartCategoryAttributeServiceImpl implements WalmartCategoryAttributeService {

    /**
     * color
     */
    private static final String COLOR = "color";

    /**
     * size
     */
    private static final String SIZE = "size";

    @Resource
    private WalmartCategoryAttributeMapper walmartCategoryAttributeMapper;
    @Resource
    private WalmartCategoryService walmartCategoryService;

    @Resource
    private WalmartNewCategoryAttributeService walmartNewCategoryAttributeService;

    @Override
    public int countByExample(WalmartCategoryAttributeExample example) {
        Assert.notNull(example, "example is null!");
        return walmartCategoryAttributeMapper.countByExample(example);
    }

    @Override
    public CQueryResult<WalmartCategoryAttribute> search(CQuery<WalmartCategoryAttributeCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        WalmartCategoryAttributeCriteria query = cquery.getSearch();
        WalmartCategoryAttributeExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = walmartCategoryAttributeMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<WalmartCategoryAttribute> walmartCategoryAttributes = walmartCategoryAttributeMapper.selectByExample(example);
        // 组装结果
        CQueryResult<WalmartCategoryAttribute> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(walmartCategoryAttributes);
        return result;
    }

    @Override
    public WalmartCategoryAttribute selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return walmartCategoryAttributeMapper.selectByPrimaryKey(id);
    }

    @Override
    public WalmartCategoryAttribute selectBySubCategoryId(String subCategoryId) {
        Assert.notNull(subCategoryId, "subCategoryId is null!");
        return walmartCategoryAttributeMapper.selectBySubCategoryId(subCategoryId);
    }

    @Override
    public List<WalmartCategoryAttribute> selectByExample(WalmartCategoryAttributeExample example) {
        Assert.notNull(example, "example is null!");
        return walmartCategoryAttributeMapper.selectByExample(example);
    }

    @Override
    public int insert(WalmartCategoryAttribute record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreateDate(new Timestamp(System.currentTimeMillis()));
        record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        return walmartCategoryAttributeMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(WalmartCategoryAttribute record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        return walmartCategoryAttributeMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(WalmartCategoryAttribute record, WalmartCategoryAttributeExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return walmartCategoryAttributeMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return walmartCategoryAttributeMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void refreshAll(HashMap<String, JSONObject> visiblePropertiesMap) {
        if(MapUtils.isEmpty(visiblePropertiesMap)) {
            return;
        }

        visiblePropertiesMap.forEach((key, value) ->{
            WalmartCategory walmartCategory = walmartCategoryService.selectBySubCategoryName(key);
            if(null == walmartCategory || StringUtils.isBlank(walmartCategory.getSubCategoryId())) {
                log.error("更新分类属性时分类表中未找到" + key);
                return;
            }
            String subCategoryId = walmartCategory.getSubCategoryId();
            WalmartCategoryAttribute newWalmartCategoryAttribute = new WalmartCategoryAttribute();
            newWalmartCategoryAttribute.setSubCategoryId(subCategoryId);
            newWalmartCategoryAttribute.setSubCategoryName(key);
            newWalmartCategoryAttribute.setAttribute(value.toString());

            WalmartCategoryAttribute walmartCategoryAttribute = this.selectBySubCategoryId(subCategoryId);
            if(null == walmartCategoryAttribute) {
                this.insert(newWalmartCategoryAttribute);
            }else {
                newWalmartCategoryAttribute.setId(walmartCategoryAttribute.getId());
                newWalmartCategoryAttribute.setCreateDate(walmartCategoryAttribute.getCreateDate());
                this.updateByPrimaryKeySelective(newWalmartCategoryAttribute);
            }
        });
    }

    @Override
    public Map<String, Map<String, Object>> matchPlatformAttribute(String categoryId, List<ProductInfo> productInfoList) {
        if (StringUtils.isBlank(categoryId) || CollectionUtils.isEmpty(productInfoList)) {
            return Collections.emptyMap();
        }

        // 返回sku对应平台属性名和属性值
        Map<String, Map<String, Object>> platformAttributeSkuMap = new HashMap<>();

        // 根据分类id获取分类属性
        WalmartCategoryAttribute walmartCategoryAttribute = selectBySubCategoryId(categoryId);
        String attribute = walmartCategoryAttribute.getAttribute();

        // 解析分类属性
        Map<String, String> platformAttributeMap = WalmartCategoryUtils.analysisCategoryAttribute(attribute);
        if (MapUtils.isEmpty(platformAttributeMap)) {
            return Collections.emptyMap();
        }

        // 如果属性没有color和size，返回空
        Set<String> platformAttribute = platformAttributeMap.keySet();
        if (!platformAttribute.contains(COLOR) && !platformAttribute.contains(SIZE)) {
            return Collections.emptyMap();
        }

        for (ProductInfo productInfo : productInfoList) {
            String sku = productInfo.getSonSku();
            String saleAttrs = productInfo.getSaleAtts();

            // 解析产品属性，过滤掉空串
            Map<String, String> prodAttributeMap = resolveAttrMap(saleAttrs);
            if (MapUtils.isEmpty(prodAttributeMap)) {
                continue;
            }

            // 匹配产品属性和平台属性
            Map<String, Object> attributeMap = WalmartCategoryUtils.matchPlatformAttribute(platformAttributeMap, prodAttributeMap);
            platformAttributeSkuMap.put(sku, attributeMap);
        }

        return platformAttributeSkuMap;
    }

    @Override
    public Map<String, Map<String, Object>> matchTemuPlatformAttribute(String categoryId, List<EsTemuItemSkuInfo> skuInfoList) {
        if (StringUtils.isBlank(categoryId) || CollectionUtils.isEmpty(skuInfoList)) {
            return Collections.emptyMap();
        }

        // 返回sku对应平台属性名和属性值
        Map<String, Map<String, Object>> platformAttributeSkuMap = new HashMap<>();

        // 根据分类id获取分类属性
        WalmartCategoryAttribute walmartCategoryAttribute = selectBySubCategoryId(categoryId);
        String attribute = walmartCategoryAttribute.getAttribute();

        // 解析分类属性
        Map<String, String> platformAttributeMap = WalmartCategoryUtils.analysisCategoryAttribute(attribute);
        if (MapUtils.isEmpty(platformAttributeMap)) {
            return Collections.emptyMap();
        }

        // 如果属性没有color和size，返回空
        Set<String> platformAttribute = platformAttributeMap.keySet();
        if (!platformAttribute.contains(COLOR) && !platformAttribute.contains(SIZE)) {
            return Collections.emptyMap();
        }

        for (EsTemuItemSkuInfo skuInfo : skuInfoList) {
            String skuId = skuInfo.getSkuId();
            List<EsAttrInfo> attrInfos = skuInfo.getAttrInfos();
            if (CollectionUtils.isEmpty(attrInfos)) {
                return Collections.emptyMap();
            }

            Map<String, String> prodAttributeMap = new HashMap<>();
            for (EsAttrInfo attrInfo : attrInfos) {
                prodAttributeMap.put(StringUtils.upperCase(attrInfo.getName()), attrInfo.getValue());
            }
            if (MapUtils.isEmpty(prodAttributeMap)) {
                continue;
            }

            // 匹配temu属性和平台属性
            Map<String, Object> attributeMap = WalmartCategoryUtils.matchPlatformAttribute(platformAttributeMap, prodAttributeMap);
            platformAttributeSkuMap.put(skuId, attributeMap);
        }

        return platformAttributeSkuMap;
    }

    private static Map<String,String> resolveAttrMap(String attributes) {
        Map<String,String> attrMap = new HashMap<>();
        if (StringUtils.isBlank(attributes)) {
            return attrMap;
        }
        JSONArray attributeArray = JSON.parseArray(attributes);
        for (int i = 0; i < attributeArray.size(); i++) {
            // 属性中包含指定的属性
            JSONObject attributeJson = attributeArray.getJSONObject(i);
            String enName = attributeJson.getString("enName");
            String enValue = attributeJson.getString("enValue");
            if (StringUtils.isNotBlank(enName) && StringUtils.isNotBlank(enValue)) {
                attrMap.put(enName.toUpperCase(), enValue);
            }
        }
        return attrMap;
    }

    @Override
    public Map<String, Map<String, Object>> matchDefaultColorAttribute(List<ProductInfo> productInfos) {
        if (CollectionUtils.isEmpty(productInfos)) {
            return Collections.emptyMap();
        }
        // 返回sku对应平台属性名和属性值
        Map<String, Map<String, Object>> platformAttributeSkuMap = new HashMap<>();
        for (ProductInfo productInfo : productInfos) {
            String sku = productInfo.getSonSku();
            String saleAttrs = productInfo.getSaleAtts();

            // 解析产品属性，过滤掉空串
            Map<String, String> prodAttributeMap = resolveAttrMap(saleAttrs);
            if (MapUtils.isEmpty(prodAttributeMap)) {
                continue;
            }
            // 合并产品属性，用-连接
            if (prodAttributeMap.size() > 1) {
                StringBuilder sb = new StringBuilder();
                for (String attribute : prodAttributeMap.keySet()) {
                    if (StringUtils.isNotBlank(prodAttributeMap.get(attribute))) {
                        sb.append(prodAttributeMap.get(attribute));
                        sb.append("-");
                    }
                }
                prodAttributeMap.put(COLOR.toUpperCase(), sb.toString());
            } else {
                prodAttributeMap.put(COLOR.toUpperCase(), prodAttributeMap.get(prodAttributeMap.keySet().iterator().next()));
            }

            Map<String, Object> attributeMap = new HashMap<>();
            attributeMap.put(COLOR, prodAttributeMap.get(COLOR.toUpperCase()));
            platformAttributeSkuMap.put(sku, attributeMap);
        }

        return platformAttributeSkuMap;
    }


    @Override
    public Map<String, Map<String, Object>> matchPlatformNewAttribute(String subCategoryName, List<ProductInfo> productInfos) {
        if (StringUtils.isBlank(subCategoryName) || CollectionUtils.isEmpty(productInfos)) {
            return Collections.emptyMap();
        }

        // 返回sku对应平台属性名和属性值
        Map<String, Map<String, Object>> platformAttributeSkuMap = new HashMap<>();

        LambdaQueryWrapper<WalmartNewCategoryAttribute> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WalmartNewCategoryAttribute::getSubCategoryName, subCategoryName);
        List<WalmartNewCategoryAttribute> walmartNewCategoryAttributes = walmartNewCategoryAttributeService.list(queryWrapper);
        if (CollectionUtils.isEmpty(walmartNewCategoryAttributes)){
            return Collections.emptyMap();
        }
        String attribute = walmartNewCategoryAttributes.get(0).getAttribute();

        // 解析分类属性
        Map<String, String> platformAttributeMap = WalmartCategoryUtils.analysisCategoryAttribute(attribute);
        if (MapUtils.isEmpty(platformAttributeMap)) {
            return Collections.emptyMap();
        }

        // 如果属性没有color和size，返回空
        Set<String> platformAttribute = platformAttributeMap.keySet();
        if (!platformAttribute.contains(COLOR) && !platformAttribute.contains(SIZE)) {
            return Collections.emptyMap();
        }

        for (ProductInfo productInfo : productInfos) {
            String sku = productInfo.getSonSku();
            String saleAttrs = productInfo.getSaleAtts();

            // 解析产品属性，过滤掉空串
            Map<String, String> prodAttributeMap = resolveAttrMap(saleAttrs);
            if (MapUtils.isEmpty(prodAttributeMap)) {
                continue;
            }

            // 匹配产品属性和平台属性
            Map<String, Object> attributeMap = WalmartCategoryUtils.matchPlatformAttribute(platformAttributeMap, prodAttributeMap);
            platformAttributeSkuMap.put(sku, attributeMap);
        }

        return platformAttributeSkuMap;
    }
}