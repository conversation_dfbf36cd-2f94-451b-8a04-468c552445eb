package com.estone.erp.publish.walmart.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.constant.PublishCommonConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.base.pms.model.StockKeepingUnitWithBLOBs;
import com.estone.erp.publish.base.pms.service.StockKeepingUnitService;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.model.CategoryMapping;
import com.estone.erp.publish.platform.model.CategoryMappingExample;
import com.estone.erp.publish.platform.service.CategoryMappingService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.platform.util.SimilarityRatioUtils;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ForbiddenAndSpecical;
import com.estone.erp.publish.system.product.bean.SpuInfo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.util.CommonMatchProdInfoUtil;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplate;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartAdminTemplateService;
import com.estone.erp.publish.walmart.componet.WalmartTemplateHelper;
import com.estone.erp.publish.walmart.componet.template.constant.WalmartTemplateConstant;
import com.estone.erp.publish.walmart.componet.template.context.WalmartTemplateValidationContext;
import com.estone.erp.publish.walmart.constant.WalmartPublishConstant;
import com.estone.erp.publish.walmart.enums.WalmartPublishRoleEnum;
import com.estone.erp.publish.walmart.enums.WalmartPublishTypeEnum;
import com.estone.erp.publish.walmart.enums.WalmartTemplateValidationEnum;
import com.estone.erp.publish.walmart.model.WalmartAccountConfig;
import com.estone.erp.publish.walmart.model.WalmartVariant;
import com.estone.erp.publish.walmart.model.dto.*;
import com.estone.erp.publish.walmart.service.*;
import com.estone.erp.publish.walmart.util.WalmartCalcPriceUtil;
import com.estone.erp.publish.walmart.util.WalmartCategoryForecastUtils;
import com.estone.erp.publish.walmart.util.WalmartItemUtils;
import com.estone.erp.publish.walmart.util.WalmartTemplateUtils;
import com.google.common.collect.Lists;
import io.vavr.collection.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.estone.erp.publish.walmart.util.WalmartTemplateUtils.getSkuReferImages;

@Slf4j
@Service
public class WalmartSingleProductTemplateNewBuilder implements WalmartTemplateNewBuilder {
    @Resource
    private StockKeepingUnitService stockKeepingUnitService;

    @Resource
    private CategoryMappingService categoryMappingService;
    @Resource
    private WalmartTemplateHelper walmartTemplateHelper;
    @Autowired
    private WalmartTemplateService walmartTemplateService;

    @Resource
    private WalmartCategoryAttributeService walmartCategoryAttributeService;

    @Resource
    private WalmartNewCategoryService walmartNewCategoryService;

    @Resource
    private WalmartAccountConfigService walmartAccountConfigService;

    @Resource
    private WalmartAdminTemplateService walmartAdminTemplateService;

    @Override
    public WalmartTemplateDTO builderTemplate(BuilderTemplateDTO builderTemplateDTO) {
        if (null == builderTemplateDTO) {
            return null;
        }

        String articleNumber = builderTemplateDTO.getArticleNumber();
        String accountNumber = builderTemplateDTO.getAccountNumber();
        List<ProductInfo> productInfos = builderTemplateDTO.getProductInfos();

        if (StringUtils.isEmpty(accountNumber) || CollectionUtils.isEmpty(productInfos)) {
            return null;
        }

        // 获取账号配置
        WalmartAccountConfig accountConfig = walmartAccountConfigService.selectByAccountNumber(accountNumber);
        if (accountConfig == null) {
            throw new BusinessException(String.format("[%s]店铺配置不存在", accountNumber));
        }




        //获取店铺后缀
        String accountSuffix = "";
        try {
            accountSuffix = walmartTemplateService.getAccountSuffix(accountNumber);
        } catch (Exception e) {
            throw new BusinessException("获取店铺后缀失败，请检查店铺配置是否正确");
        }

        if (StringUtils.isBlank(accountSuffix)) {
            throw new BusinessException("获取店铺为空，请检查店铺配置是否正确");
        }

        List<StockKeepingUnitWithBLOBs> skuInfoList = Optional.ofNullable(stockKeepingUnitService.handleProductInfo(productInfos)).orElse(Collections.emptyList());

        // 根据店铺配置上架拦截
        List<String> skuList = skuInfoList.stream().map(StockKeepingUnitWithBLOBs::getArticleNumber).distinct().collect(Collectors.toList());
        List<String> filteredSkuList = WalmartItemUtils.filterSkuListByAccountConfig(accountNumber, skuList);
        skuInfoList.removeIf(skuInfo -> !filteredSkuList.contains(skuInfo.getArticleNumber()));
        if (CollectionUtils.isEmpty(skuInfoList)) {
            throw new RuntimeException("根据店铺配置上架拦截:无符合的sku");
        }

        // 仅保留剩下的的sku
        productInfos = productInfos.stream().filter(productInfo -> filteredSkuList.contains(productInfo.getSonSku())).collect(Collectors.toList());

        WalmartTemplateDTO templateDTO = new WalmartTemplateDTO();
        templateDTO.setBrand(accountConfig.getBrand());
        ProductInfo productInfo = productInfos.get(0);
        //运费模板物流中心
        templateDTO.setShippingTemplate(accountConfig.getSelectedShippingTemplate());
        templateDTO.setFulfillmentCenter(accountConfig.getSelectedFulfillmentCenter());

        templateDTO.setInventory(accountConfig.getDefaultStock());
        templateDTO.setPublishRole(WalmartPublishRoleEnum.SALE.getCode());
        templateDTO.setPublishType(WalmartPublishTypeEnum.AUTO_PUBLISH.getCode());

        //优先类目映射再预测
//        if (StringUtils.isBlank(builderTemplateDTO.getSubCategoryId())) {
//            Pair<String, String> subCategoryPair = getSubCategoryPair(productInfo.getFullpathcode(), articleNumber);
//            if (subCategoryPair != null) {
//                templateDTO.setSubCategoryId(subCategoryPair.getLeft());
//                templateDTO.setSubCategoryName(subCategoryPair.getRight());
//            }
//        } else {
//            WalmartNewCategory walmartNewCategory = walmartNewCategoryService.selectBySubCategoryId(builderTemplateDTO.getSubCategoryId());
//            if (!ObjectUtils.isEmpty(walmartNewCategory)) {
//                templateDTO.setSubCategoryName(walmartNewCategory.getCatEnglishName());
//                templateDTO.setSubCategoryId(walmartNewCategory.getCatId().toString());
//            } else {
//                throw new BusinessException("查询不到改该分类！");
//            }
//        }
        // ES-13061 刊登使用固定分类，"subCategoryId": *********, "subCategoryName": "Hardware Washers",
        templateDTO.setSubCategoryId(WalmartTemplateConstant.DEFAULT_CATEGORY_ID);
        templateDTO.setSubCategoryName(WalmartTemplateConstant.DEFAULT_CATEGORY_NAME);


        templateDTO.setIsParent(builderTemplateDTO.getIsParent());

        templateDTO.setAccountNumber(accountNumber);

        //sellerSku
        templateDTO.setSellerSku(articleNumber+ "_" + accountSuffix);

        // 货号
        templateDTO.setArticleNumber(articleNumber);

        // 数据来源
        templateDTO.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());

        // 前端展示：单品状态
        String itemStatusStr = productInfos.stream().map(ProductInfo::getItemStatus).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
        templateDTO.setItemStatus(itemStatusStr);

        // 前端展示：禁售平台
        String saleForbiddenPlatformStr = productInfos.stream().map(ProductInfo::getSaleForbiddenList).filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
        templateDTO.setSaleForbiddenPlatform(saleForbiddenPlatformStr);


        // 前端展示：上贴备注/刊登备注
        String postRemarkStr = productInfos.stream().map(ProductInfo::getPostRemark).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
        templateDTO.setPostRemark(postRemarkStr);

        // 前端展示：标签
        String tagStr = productInfos.stream().map(ProductInfo::getTag).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
        templateDTO.setTag(tagStr);

        // 图片
        templateDTO.setImageList(FmsUtils.getAmazonImages(articleNumber));

        // 是否是变体
        Boolean saleVariant = articleNumber.equals(productInfo.getMainSku())
                && !ObjectUtils.nullSafeEquals(productInfo.getMainSku(), productInfo.getSonSku());
        templateDTO.setSaleVariant(saleVariant);

        //匹配admin范本类目属性
//        String categoryAttribute = this.matchCategoryAttribute(articleNumber, templateDTO.getSubCategoryId());
        templateDTO.setCategoryAttribute(WalmartTemplateConstant.DEFAULT_CATEGORY_ATTRIBUTE);

        if (!saleVariant) {
            templateDTO.setShippingWeight(getSkuShippingWeight(skuInfoList.get(0)));
            String mainImageUrl = WalmartTemplateUtils.getSkuMainImage(articleNumber, templateDTO.getImageList());
            templateDTO.setMainImageUrl(mainImageUrl);
            //附图
            List<String> skuReferImages = getSkuReferImages(articleNumber, null, templateDTO.getImageList());
            templateDTO.setExtraImageUrls(JSON.toJSONString(skuReferImages));
            //计算价格
            calculatePrice(templateDTO);
        } else {
            // 获取分类属性属性
            Map<String, Map<String, Object>> attributeMap = walmartCategoryAttributeService.matchDefaultColorAttribute(productInfos);

            List<WalmartVariant> walmartVariants = new ArrayList<>(skuInfoList.size());
            for (StockKeepingUnitWithBLOBs item : skuInfoList) {
                String itemArticleNumber = item.getArticleNumber();
                if (StringUtils.isBlank(itemArticleNumber)) {
                    continue;
                }
                WalmartVariant walmartVariant = new WalmartVariant();
                walmartVariants.add(walmartVariant);
                walmartVariant.setSku(itemArticleNumber);
                walmartVariant.setSellerSku(itemArticleNumber + "_" + accountSuffix);
                walmartVariant.setItemStatus(item.getSkulifecyclephase());
                walmartVariant.setSaleForbiddenPlatform(item.getForbiddensalechannel());
                walmartVariant.setSpu(productInfo.getMainSku());
                walmartVariant.setVariantGroupId(articleNumber + "_" + accountSuffix);
                walmartVariant.setInventory(accountConfig.getDefaultStock());

                WalmartTemplateUtils.setTempalterImage(walmartVariant, templateDTO.getImageList());

                // 重量
                walmartVariant.setShippingWeight(getSkuShippingWeight(item));

                // 分类属性
                walmartVariant.setAttributeMap(attributeMap.getOrDefault(itemArticleNumber, new HashMap<>()));

                //第一个是主变体
                walmartVariant.setIsPrimaryVariant(walmartVariants.indexOf(walmartVariant) == 0);

            }
            //计算价格
            calculatePriceByVariants(templateDTO.getAccountNumber(),walmartVariants);
            templateDTO.setWalmartVariantList(walmartVariants);
        }

        // 匹配标题、描述、五点描述
        WalmartTemplateUtils.matchingTemplateInfoNew(productInfo.getMainSku(), templateDTO);

        // 沃尔玛标题差异化:相同程度大于90%的判断，大于90%就去取词增加关键词，过滤包含数字的关键词
        walmartTitleDifferentiation(templateDTO);

        //生成GTIN
        this.setGtin(templateDTO);

        return templateDTO;
    }

    private void calculatePriceByVariants(String accountNumber, List<WalmartVariant> walmartVariants) {
        if (CollectionUtils.isEmpty(walmartVariants)){
            return;
        }
        List<String> skus = walmartVariants.stream().map(WalmartVariant::getSku).collect(Collectors.toList());
        WalmartTemplateCalcPriceRequest request = new WalmartTemplateCalcPriceRequest();
        request.setAccountNumber(accountNumber);
        request.setArticleNumberList(skus);
        request.setGrossProfitRate(null);
        request.setShippingMethod("CKY-USKXD");
        ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = WalmartCalcPriceUtil.calcTemplatePrice(request);
        if (!listApiResult.isSuccess()){
            throw new BusinessException("算价失败:"+ listApiResult.getErrorMsg());
        }
        List<BatchPriceCalculatorResponse> responseList = listApiResult.getResult();
        if (CollectionUtils.isEmpty(responseList)){
            throw new BusinessException("算价失败,请求算价结果为空");
        }
        Map<String, BatchPriceCalculatorResponse> responseMap = responseList.stream().collect(Collectors.toMap(BatchPriceCalculatorResponse::getArticleNumber, o->o, (k1,k2)->k1));
        for (WalmartVariant walmartVariant : walmartVariants) {
            String sku = walmartVariant.getSku();
            BatchPriceCalculatorResponse batchPriceCalculatorResponse = responseMap.get(sku);
            if (batchPriceCalculatorResponse == null){
                throw new BusinessException("算价失败,请求算价结果为空");
            }
            Double foreignPrice = batchPriceCalculatorResponse.getForeignPrice();
            //foreignPrice四舍五入保留两位
            walmartVariant.setPrice(BigDecimal.valueOf(Double.parseDouble(new DecimalFormat("0.00").format(foreignPrice))).setScale(2, RoundingMode.HALF_UP).doubleValue());
        }
    }

    private void calculatePrice(WalmartTemplateDTO templateDTO) {
        WalmartTemplateCalcPriceRequest request = new WalmartTemplateCalcPriceRequest();
        request.setAccountNumber(templateDTO.getAccountNumber());
        request.setArticleNumberList(Arrays.asList(templateDTO.getArticleNumber()));
        request.setGrossProfitRate(null);
        request.setShippingMethod("CKY-USKXD");
        ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = WalmartCalcPriceUtil.calcTemplatePrice(request);
        if (!listApiResult.isSuccess()){
            throw new BusinessException("算价失败:"+ listApiResult.getErrorMsg());
        }
        List<BatchPriceCalculatorResponse> responseList = listApiResult.getResult();
        if (CollectionUtils.isEmpty(responseList)){
            throw new BusinessException("算价失败,请求算价结果为空");
        }
        BatchPriceCalculatorResponse batchPriceCalculatorResponse = responseList.get(0);
        Double foreignPrice = batchPriceCalculatorResponse.getForeignPrice();
        //foreignPrice四舍五入保留两位
        templateDTO.setPrice(BigDecimal.valueOf(Double.parseDouble(new DecimalFormat("0.00").format(foreignPrice))).setScale(2, RoundingMode.HALF_UP).doubleValue());
    }

    private void setGtin(WalmartTemplateDTO templateDTO) {
        AccountEanRequest accountEanRequest = buildEanRequest(templateDTO);
        if (accountEanRequest == null) {
            return;
        }

        List<String> gtinList = walmartTemplateService.generateProductId(accountEanRequest);
        if (CollectionUtils.isEmpty(gtinList)) {
            return;
        }

        if (BooleanUtils.isTrue(templateDTO.getSaleVariant())) {
            setVariantGtin(templateDTO.getWalmartVariantList(), gtinList);
        } else {
            templateDTO.setProductId(gtinList.get(0));
        }
        templateDTO.setProductIdType("GTIN");
    }

    private AccountEanRequest buildEanRequest(WalmartTemplateDTO templateDTO) {
        List<String> skuList;
        int size;

        if (BooleanUtils.isTrue(templateDTO.getSaleVariant())) {
            List<WalmartVariant> variants = templateDTO.getWalmartVariantList();
            if (CollectionUtils.isEmpty(variants)) {
                return null;
            }
            skuList = variants.stream()
                    .map(WalmartVariant::getSku)
                    .collect(Collectors.toList());
            size = skuList.size();
        } else {
            skuList = Arrays.asList(templateDTO.getArticleNumber());
            size = 1;
        }

        AccountEanRequest request = new AccountEanRequest();
        request.setSellerId(templateDTO.getAccountNumber());
        request.setSkuList(skuList);
        request.setCardCodeType("GTIN");
        request.setSize(size);
        return request;
    }

    private void setVariantGtin(List<WalmartVariant> variants, List<String> gtinList) {
        for (int i = 0; i < variants.size(); i++) {
            String gtin = gtinList.get(i);
            if (StringUtils.isNotBlank(gtin)) {
                variants.get(i).setProductId(gtin);
            }
        }
    }


    private String matchCategoryAttribute(String articleNumber, String subCategoryId) {
        if (StringUtils.isBlank(subCategoryId) || StringUtils.isBlank(articleNumber)) {
            return null;
        }

        LambdaQueryWrapper<WalmartAdminTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WalmartAdminTemplate::getSubCategoryId, subCategoryId);
        queryWrapper.eq(WalmartAdminTemplate::getArticleNumber, articleNumber);
        queryWrapper.eq(WalmartAdminTemplate::getStatus, 1);
        List<WalmartAdminTemplate> list = walmartAdminTemplateService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0).getCategoryAttribute();
        }
        return null;

    }


    /**
     * 沃尔玛标题差异化：相同程度大于90%的判断，大于90%就去取词增加关键词，过滤包含数字的关键词
     *
     * @param template
     */
    public void walmartTitleDifferentiation(WalmartTemplateDTO template) {
        String articleNumber = template.getArticleNumber();

        if (StringUtils.isBlank(articleNumber)) {
            return;
        }

        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(Collections.singletonList(articleNumber));
        if (CollectionUtils.isEmpty(productInfoList)) {
            return;
        }
        Optional<String> desEnOptional = productInfoList.stream().map(ProductInfo::getDesEn).filter(StringUtils::isNotBlank).findFirst();
        if (desEnOptional.isEmpty()) {
            return;
        }

        float similarityRatio = SimilarityRatioUtils.getSimilarityRatio(desEnOptional.get().toLowerCase().toLowerCase(), template.getTitle().toLowerCase());
        if (similarityRatio <= 0.9) {
            return;
        }

        //-- 查询spu信息
        ResponseJson rsp = ProductUtils.findSpuInfo(articleNumber);
        if (!rsp.isSuccess()) {
            throw new RuntimeException(rsp.getMessage());
        }
        List<SpuInfo> spuInfos = (List<SpuInfo>) rsp.getBody().get(ProductUtils.resultKey);
        if (CollectionUtils.isEmpty(spuInfos)) {
            throw new RuntimeException("获取spu信息为空！");
        }
        SpuInfo spuInfo = spuInfos.get(0);

        Set<String> searchDatasSet = new HashSet<>();

        if (StringUtils.isNotEmpty(spuInfo.getPublishKeyWord())) {
            List<String> searchDataList = JSONArray.parseArray(spuInfo.getPublishKeyWord(), String.class);
            for (String string : searchDataList) {
                String s = string.replaceAll("\n", ",").replaceAll("\r", ",");
                List<String> strings1 = CommonUtils.splitList(s, ",");
                for (String s1 : strings1) {
                    if (!s1.contains(":")) {
                        searchDatasSet.add(s1);
                    }
                }
            }
        }

        // 沃尔玛标题差异化自动组合关键词
        String title = CommonMatchProdInfoUtil.diffTitleAutoKeyword(template.getTitle(), searchDatasSet);
        if (StringUtils.isNotBlank(title)) {
            template.setTitle(title);
        }
    }


    /**
     * 获取分类
     * 产品分类取值规则：优先取分类映射的数据，若SPU对应分类映射数据为空，则通过SPU+中文标题获取类目预测数据
     *
     * @param systemCategoryId
     * @param articleNumber
     * @return
     */
    public Pair<String, String> getSubCategoryPair(String systemCategoryId, String articleNumber) {
        String subCategoryId = null;
        String subCategoryName = null;
        // 产品分类取值规则：优先取分类映射的数据，若SPU对应分类映射数据为空，则通过SPU+中文标题获取类目预测数据
        if (StringUtils.isNotBlank(systemCategoryId)) {
            // 获取分类映射
            CategoryMappingExample example = new CategoryMappingExample();
            example.createCriteria().andPlatformEqualTo(Platform.Walmart.name())
                    .andSystemCategoryIdEqualTo(systemCategoryId).andApplyStateEqualTo(ApplyStatusEnum.YES.getCode());
            List<CategoryMapping> categoryMappings = categoryMappingService.selectByExample(example);
            if (CollectionUtils.isNotEmpty(categoryMappings)) {
                CategoryMapping categoryMapping = categoryMappings.get(0);
                subCategoryId = categoryMapping.getPlatformCategoryId();
                String platformCategoryName = categoryMapping.getPlatformCategoryName();

                if (StringUtils.isNotBlank(platformCategoryName)) {
                    subCategoryName = platformCategoryName.substring(platformCategoryName.lastIndexOf(">") + 1).trim();
                }

            }
        }

        if (StringUtils.isBlank(subCategoryName)) {
            // 调用预测接口
            return WalmartCategoryForecastUtils.setCategoryByForecast(
                    articleNumber);

        }
        return Pair.of(subCategoryId, subCategoryName);
    }

    @Override
    public BuilderTemplateDTO validationProductData(WalmartTemplateValidationContext context) {
        String articleNumber = context.getArticleNumber();
        String accountNumber = context.getAccountNumber();
        WalmartTemplateDTO templateDTO = context.getTemplateDTO();
        // 根据货号获取产品信息
        List<ProductInfo> productInfoList;
        if (templateDTO == null || CollectionUtils.isEmpty(templateDTO.getWalmartVariantList())) {
            productInfoList = ProductUtils.findProductInfos(Lists.newArrayList(articleNumber));
        } else {
            // 如果传入的模板数据不为空，则需根据模板的sku查产品信息
            List<WalmartVariant> walmartVariants = templateDTO.getWalmartVariantList();
            List<String> skuList = walmartVariants.stream().map(WalmartVariant::getSku).collect(Collectors.toList());
            productInfoList = ProductUtils.findProductInfos(skuList);
        }
        if (CollectionUtils.isEmpty(productInfoList)) {
            throw new NoSuchElementException("查询不到产品信息:" + articleNumber);
        }

        // 校验sku状态 停产、存档、废弃
        List<ProductInfo> filterBadStatusProducts = productInfoList.stream()
                .filter(productInfo -> StringUtils.isNotBlank(productInfo.getItemStatus()))
                .filter(productInfo -> !PublishCommonConstant.INTERCEPT_EN_STATE_LIST.contains(productInfo.getItemStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterBadStatusProducts)) {
            context.addValidationData(WalmartTemplateValidationDTO.failOf(WalmartTemplateValidationEnum.NO_PUBLISH_SKU_STATUS,
                    "单品状态为停产存档废弃，不可刊登", null));
        } else {
            context.addValidationData(new WalmartTemplateValidationDTO(WalmartTemplateValidationEnum.NO_PUBLISH_SKU_STATUS, true));
        }


        // 获取账号配置
        WalmartAccountConfig accountConfig = walmartAccountConfigService.selectByAccountNumber(accountNumber);
        if (accountConfig == null) {
            throw new BusinessException(String.format("[%s]店铺配置不存在", accountNumber));
        }

        List<ProductInfo> productInfos = filterBadStatusProducts;


        // 处理产品信息
        if (BooleanUtils.isTrue(accountConfig.getSpecialSupplyShop())) {
            // 特供店铺处理逻辑
            List<String> skuList = productInfos.stream().map(ProductInfo::getSonSku).distinct().collect(Collectors.toList());
            // 获取店铺配置的特供标签code
            List<Integer> specialSupplyCodesList = CommonUtils.splitList(accountConfig.getSpecialSupplyCodes(), ",")
                    .stream().map(s -> Integer.valueOf(s.trim())).collect(Collectors.toList());
            // 获取特殊商品代码映射
            Map<String, ForbiddenAndSpecical> specialGoodsCodeMap = ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSkuBatch(skuList, 250);

            // 按是否为包含店铺特供标签分组
            Map<Boolean, List<ProductInfo>> containsMap = productInfos.stream().collect(Collectors.groupingBy(productInfo -> {
                List<Integer> specialGoodsCodeList = Optional.ofNullable(specialGoodsCodeMap.get(productInfo.getSonSku()))
                        .map(ForbiddenAndSpecical::getSpecialGoodsTypes).orElse(Collections.emptyList());
                return WalmartItemUtils.isContainsShopSpecialTag(specialGoodsCodeList, specialSupplyCodesList);
            }));

            // 只对不包含店铺特供标签的产品进行禁售检查
            List<ProductInfo> validatedProducts = checkSalesProhibition(containsMap.getOrDefault(false, Collections.emptyList()));

            // 合并通过禁售检查的产品和特供标签的产品
            productInfos = Stream.concat(validatedProducts, containsMap.getOrDefault(true, Collections.emptyList())).collect(Collectors.toList());
        } else {
            // 非特供店铺处理逻辑 - 直接检查所有产品
            productInfos = checkSalesProhibition(CollectionUtils.isEmpty(filterBadStatusProducts) ? productInfoList : filterBadStatusProducts);
        }

        // 添加验证结果
        if (CollectionUtils.isEmpty(productInfos)) {
            context.addValidationData(WalmartTemplateValidationDTO.failOf(WalmartTemplateValidationEnum.SITE_BAN,
                    "SKU标记Amazon或SMT或Ebay或Wish或Walmart且US站点侵权禁售，不可刊登", null));
        } else {
            context.addValidationData(new WalmartTemplateValidationDTO(WalmartTemplateValidationEnum.SITE_BAN, true));
        }

        // 校验重复刊登
        walmartTemplateHelper.checkRepeatPublication(context);


        return BuilderTemplateDTO.builder()
                .articleNumber(articleNumber)
                .skuDataSource(context.getSkuDataSource())
                .accountNumber(context.getAccountNumber())
                .productInfos(productInfos)
                .subCategoryId(context.getSubCategoryId())
                .isParent(context.getIsParent())
                .build();
    }


    private List<ProductInfo> checkSalesProhibition(List<ProductInfo> productInfoList) {
        return Optional.ofNullable(productInfoList).orElse(Collections.emptyList()).stream().filter(productInfo -> {
            List<SalesProhibitionsVo> salesProhibitionsVos = productInfo.getSalesProhibitionsVos();

            return CollectionUtils.isEmpty(salesProhibitionsVos) || salesProhibitionsVos.stream()
                    .filter(salesProhibitionsVo -> WalmartPublishConstant.PUBLISH_INTERCEPT_CHANNEL.contains(salesProhibitionsVo.getPlat()))
                    .noneMatch(salesProhibitionsVo ->
                            salesProhibitionsVo.getSites().stream()
                                    .map(Sites::getSite)
                                    .anyMatch(WalmartPublishConstant.PUBLISH_INTERCEPT_SITE::contains));


        }).collect(Collectors.toList());
    }


    /**
     * 计算重量（自身净重+包材重量+搭配包材重量+面单重量3g）平台计量单位是磅  1克=0.002204623磅
     *
     * @param item
     * @return
     */
    private Double getSkuShippingWeight(StockKeepingUnitWithBLOBs item) {
        // 自身净重
        double skuWeight = item.getWeight() == null ? 0.0 : item.getWeight();

        // 包材重量 + 搭配包材重量
        double pmWeight = item.getPmWeight() == null ? 0.0 : item.getPmWeight();

        return NumberUtils.format((skuWeight + pmWeight + 3) * 0.002204623, "0.###");
    }

}
