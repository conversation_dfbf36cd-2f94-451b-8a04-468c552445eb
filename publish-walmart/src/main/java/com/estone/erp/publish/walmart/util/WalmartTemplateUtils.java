package com.estone.erp.publish.walmart.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.WalmartExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.util.CommonMatchProdInfoUtil;
import com.estone.erp.publish.walmart.constant.WalmartPublishConstant;
import com.estone.erp.publish.walmart.enums.WalmartTemplateTableEnum;
import com.estone.erp.publish.walmart.model.WalmartImageOSSBean;
import com.estone.erp.publish.walmart.model.WalmartTemplate;
import com.estone.erp.publish.walmart.model.WalmartVariant;
import com.estone.erp.publish.walmart.model.dto.TitleDescription;
import com.estone.erp.publish.walmart.model.dto.WalmartTemplateDTO;
import com.estone.erp.publish.walmart.model.vo.WalmartTemplateSkuInfoVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/13 16:30
 */
@Slf4j
public class WalmartTemplateUtils {


    private static SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);


    /**
     * 查询产品信息，并过滤停产存档废弃，以及禁售的产品
     *
     * @param articleNumber
     * @return
     */
    public static List<ProductInfo> getTemplateSkuInfo(String articleNumber) {
        if (StringUtils.isBlank(articleNumber)) {
            return Collections.emptyList();
        }

        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(Lists.newArrayList(articleNumber));
        if (CollectionUtils.isEmpty(productInfoList)) {
            throw new RuntimeException("查询不到产品信息");
        }

        // 如果参数货号是子sku，则以单体形式返回
        if (!articleNumber.equalsIgnoreCase(productInfoList.get(0).getMainSku())) {
            productInfoList = productInfoList
                    .stream().filter(o -> articleNumber.equalsIgnoreCase(o.getSonSku())).collect(Collectors.toList());
        }

        List<ProductInfo> resultList = new ArrayList<>();
        for (ProductInfo productInfo : productInfoList) {
            // 停产，存档，废弃的SKU过滤
            if (SkuStatusEnum.isContainIllegalStatus(productInfo.getItemStatus())) {
                continue;
            }

            List<SalesProhibitionsVo> salesProhibitionsVos = productInfo.getSalesProhibitionsVos();
            if (CollectionUtils.isEmpty(salesProhibitionsVos)) {
                resultList.add(productInfo);
                continue;
            }

            boolean forbidden = salesProhibitionsVos.stream()
                    .filter(salesProhibitionsVo -> WalmartPublishConstant.PUBLISH_INTERCEPT_CHANNEL.contains(salesProhibitionsVo.getPlat()))
                    .anyMatch(salesProhibitionsVo ->
                            salesProhibitionsVo.getSites().stream()
                                    .map(Sites::getSite)
                                    .anyMatch(WalmartPublishConstant.PUBLISH_INTERCEPT_SITE::contains));
            if (forbidden) {
                continue;
            }
            resultList.add(productInfo);

        }

        if (CollectionUtils.isEmpty(resultList)) {
            throw new RuntimeException("SKU均为停产存档废弃或者在walmart禁售");
        }

        return resultList;
    }

    /**
     * 匹配标题、描述、五点描述
     *
     * @param mainSku
     * @param walmartTemplateSkuInfoVO
     */
    public static void matchingTemplateInfo(String mainSku, WalmartTemplateSkuInfoVO walmartTemplateSkuInfoVO) {
        SpuOfficial amazonSpuOfficial = ProductUtils.getAmazonOfficial(mainSku);
        // 获取标题描述五点描述
        TitleDescription titleDescription = WalmartCommonUtils.matchingAmazonSpuTitleDescription(amazonSpuOfficial);
        if (titleDescription == null) {
            SpuOfficial spuOfficial = getSpuOfficial(mainSku);
            titleDescription = WalmartCommonUtils.matchingSpuTitleDescription(spuOfficial, true);
            if (titleDescription == null) {
                titleDescription = WalmartCommonUtils.matchingTitleDescription(spuOfficial, amazonSpuOfficial);
            }
        }
        walmartTemplateSkuInfoVO.setTitle(titleDescription.getTitle());
        walmartTemplateSkuInfoVO.setDescription(titleDescription.getDescription());
        walmartTemplateSkuInfoVO.setKeyFeatures(titleDescription.getKeyFeatures());

        List<WalmartVariant> walmartVariantList = walmartTemplateSkuInfoVO.getWalmartVariantList();

        String attributeStr = null;
        if (CollectionUtils.isNotEmpty(walmartVariantList)) {
            attributeStr = WalmartTemplateUtils.getAttributeStr(walmartVariantList);
        }

        // 校验侵权词
        Map<String, String> infringingWords = checkInfringementWords(titleDescription.getTitle(),
                titleDescription.getDescription(), null, titleDescription.getKeyFeatures(), attributeStr);
        if (MapUtils.isNotEmpty(infringingWords)) {
            walmartTemplateSkuInfoVO.setInfringingWords(infringingWords);
        }
    }

    private static SpuOfficial getSpuOfficial(String mainSku) {
        ResponseJson resp = ProductUtils.getSpuTitles(Lists.newArrayList(mainSku));
        SpuOfficial spuOfficial = null;
        if (resp.isSuccess()) {
            List<SpuOfficial> spuInfos = (List<SpuOfficial>) resp.getBody().get(ProductUtils.resultKey);
            if (CollectionUtils.isNotEmpty(spuInfos)) {
                spuOfficial = spuInfos.get(0);
            }
        }
        return spuOfficial;
    }

    public static Map<String, String> checkInfringementWords(String title, String description, String brand, String keyFeatures, String attributeMsg) {
        if (StringUtils.isNotBlank(keyFeatures)) {
            List<String> keyFeatureList = JSON.parseArray(keyFeatures, String.class);
            keyFeatures = Joiner.on(StrConstant.CHECK_INFRING_WORD_SPLIT).join(keyFeatureList);
        }
        Map<String, String> resultMap = new ConcurrentHashMap<>();
        CompletableFuture<Void> titleFuture = WalmartExecutors.submitAsyncTortTask(() -> {
            List<String> titleInfringementList = WalmartInfringementWordUtils.getInfringementWords(title);
            if (CollectionUtils.isNotEmpty(titleInfringementList)) {
                resultMap.put("标题", StringUtils.join(titleInfringementList, ","));
            }
        });
        CompletableFuture<Void> descriptionFuture = WalmartExecutors.submitAsyncTortTask(() -> {
            List<String> descriptionInfringementList = WalmartInfringementWordUtils.getInfringementWords(description);
            if (CollectionUtils.isNotEmpty(descriptionInfringementList)) {
                resultMap.put("描述", StringUtils.join(descriptionInfringementList, ","));
            }
        });
        String finalKeyFeatures = keyFeatures;
        CompletableFuture<Void> bulletPointFuture = WalmartExecutors.submitAsyncTortTask(() -> {
            List<String> featuresInfringementList = WalmartInfringementWordUtils.getInfringementWords(finalKeyFeatures);
            if (CollectionUtils.isNotEmpty(featuresInfringementList)) {
                resultMap.put("五点描述", StringUtils.join(featuresInfringementList, ","));
            }
        });

        CompletableFuture<Void> attributeFuture = WalmartExecutors.submitAsyncTortTask(() -> {
            List<String> featuresInfringementList = WalmartInfringementWordUtils.getInfringementWords(attributeMsg);
            if (CollectionUtils.isNotEmpty(featuresInfringementList)) {
                resultMap.put("属性", StringUtils.join(featuresInfringementList, ","));
            }
        });

        CompletableFuture<Void> brandFuture = WalmartExecutors.submitAsyncTortTask(() -> {
            List<String> featuresInfringementList = WalmartInfringementWordUtils.getInfringementWords(brand);
            if (CollectionUtils.isNotEmpty(featuresInfringementList)) {
                resultMap.put("品牌", StringUtils.join(featuresInfringementList, ","));
            }
        });

        // 等待任务全部执行完毕
        CompletableFuture.allOf(titleFuture, descriptionFuture, bulletPointFuture, attributeFuture, brandFuture).join();
        if (MapUtils.isNotEmpty(resultMap)) {
            return resultMap;
        }
        return null;
    }

    /**
     * 范本模板分表
     *
     * @param isParent
     * @return
     */
    public static String getTemplateTable(Boolean isParent) {
        String table;
        if (BooleanUtils.isTrue(isParent)) {
            table = WalmartTemplateTableEnum.WALMART_TEMPLATE_MODEL.getCode();
        } else {
            table = WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode();
        }
        return table;
    }


    /**
     * 获取oss图片路径
     *
     * @param walmartTemplates
     */
    public static void setOSSUrl(List<WalmartTemplate> walmartTemplates) {
        if (CollectionUtils.isEmpty(walmartTemplates)) {
            return;
        }
        // 上传图片
        String urlIsUseAcceleratedEndpoint = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "OSS_URL_USE_ACCELERATED_ENDPOINT", 10);
        String uploadIsUseAcceleratedEndpoint = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "OSS_UPLOAD_USE_ACCELERATED_ENDPOINT", 10);
        boolean urlIsUseAcceleratedEndpointFalg = "true".equalsIgnoreCase(urlIsUseAcceleratedEndpoint) ? true : false;
        boolean uploadIsUseAcceleratedEndpointFalg = "true".equalsIgnoreCase(uploadIsUseAcceleratedEndpoint) ? true : false;
        for (WalmartTemplate walmartTemplate : walmartTemplates) {
            try {
                // 获取模板图片
                Set<String> templateImageSet = new HashSet<>();
                if (walmartTemplate.getSaleVariant()) {
                    List<WalmartVariant> walmartVariants = JSONObject.parseArray(walmartTemplate.getVariations(), WalmartVariant.class);
                    for (WalmartVariant walmartVariant : walmartVariants) {
                        templateImageSet.add(walmartVariant.getMainImageUrl());
                        templateImageSet.add(walmartVariant.getSwatchImageUrl());
                        templateImageSet.addAll(JSONObject.parseArray(walmartVariant.getExtraImageUrls(), String.class));
                    }
                } else {
                    templateImageSet.add(walmartTemplate.getMainImageUrl());
                    templateImageSet.addAll(JSONObject.parseArray(walmartTemplate.getExtraImageUrls(), String.class));
                }

                // 获取图片路径
                String url = WalmartAccountUtils.getImagePath(walmartTemplate.getAccountNumber());


                WalmartImageOSSBean walmartImageOSSBean = new WalmartImageOSSBean();
                walmartImageOSSBean.setProductSku(walmartTemplate.getId().toString());
                walmartImageOSSBean.setImagePath(url);
                walmartImageOSSBean.setImageUrls(new ArrayList(templateImageSet));
                walmartImageOSSBean.setUrlIsUseAcceleratedEndpoint(urlIsUseAcceleratedEndpointFalg);
                walmartImageOSSBean.setUploadIsUseAcceleratedEndpoint(uploadIsUseAcceleratedEndpointFalg);
                if (WalmartAccountUtils.isPublishTemuAccount(walmartTemplate.getAccountNumber())) {
                    walmartImageOSSBean.setIsTemu(true);
                }

                Map<String, String> imageMappingMap;
                if (ObjectUtils.isEmpty(walmartTemplate.getImageServer()) || walmartTemplate.getImageServer().equals(0)) {
                    imageMappingMap = WalmartTencentCosUtils
                            .uploadImagesToTencentCos(walmartImageOSSBean, false);
                } else {
                    walmartImageOSSBean.setUrlIsUseAcceleratedEndpoint(true);
                    walmartImageOSSBean.setUrlIsUseAcceleratedEndpoint(true);
                    imageMappingMap = WalmartAliOSSUtils.copyImagesToAliOSS(walmartImageOSSBean, false);
                }

                walmartTemplate.setImageMappingMap(imageMappingMap);
            } catch (Exception e) {
                throw new RuntimeException(String.format("模板获取oss图片报错：%s", e.getMessage()));
            }
        }
    }

    /**
     * 处理标题描述
     *
     * @param walmartTemplate 模板
     */
    public static void handleTitleDescription(WalmartTemplate walmartTemplate) {
        String title = walmartTemplate.getTitle();
        if (StringUtils.isNotBlank(title)) {
            title = title.trim().replaceAll("\\s+", " ");
            walmartTemplate.setTitle(title);
        }

        String keyFeatures = walmartTemplate.getKeyFeatures();
        if (StringUtils.isNotBlank(keyFeatures)) {
            List<String> keyFeatureList = JSON.parseArray(keyFeatures, String.class);
            List<String> newKeyFeatureList = new ArrayList<>();
            for (String keyFeature : keyFeatureList) {
                if (StringUtils.isBlank(keyFeature)) {
                    continue;
                }
                keyFeature = keyFeature.trim().replaceAll("\\s+", " ");
                if (StringUtils.isBlank(keyFeature)) {
                    continue;
                }
                newKeyFeatureList.add(keyFeature);
            }
            walmartTemplate.setKeyFeatures(JSON.toJSONString(newKeyFeatureList));
        }
    }

    public static void setTempalterImage(WalmartVariant walmartVariant, List<String> imageList) {
        if (CollectionUtils.isEmpty(imageList)) {
            return;
        }

        //获取主图或样例图
        String mainImageUrl = getSkuMainImage(walmartVariant.getSku(), imageList);
        walmartVariant.setMainImageUrl(mainImageUrl);
        walmartVariant.setSwatchImageUrl(mainImageUrl);

        //获取附图
        List<String> skuReferImages = getSkuReferImages(walmartVariant.getSpu(), walmartVariant.getSku(), imageList);
        walmartVariant.setExtraImageUrls(JSON.toJSONString(skuReferImages));

    }


    /**
     * 根据SKU图片进行取值，-cmb和-effect，优先取SKU-cmb，SKU-effect，若无，则取SPU-cmb，SPU-effect，排除其他子SKU的图片
     *
     * @param spu    SPU编号，用于匹配图片
     * @param sku    SKU编号，用于匹配图片
     * @param images 产品图片池，包含所有可选图片的列表
     * @return 附图集合
     */
    public static List<String> getSkuReferImages(String spu, String sku, List<String> images) {
        if (CollectionUtils.isEmpty(images)) {
            return Collections.emptyList();
        }

        // 获取当前spu下的sku列表信息
        List<String> skuList = singleItemEsService.getSonSkuListByMainSku(Collections.singletonList(spu));

        // 包含sku集合则为变体，否则为单体
        if (CollectionUtils.isNotEmpty(skuList) && ObjectUtils.isNotEmpty(sku)) {
            // 需要通过其它sku过滤掉不属于他们的图片，然后随机取这里的图片
            List<String> newImages = images.stream()
                    .filter(image -> image.contains(sku) || skuList.stream().noneMatch(skuStr -> image.contains(skuStr)))
                    .collect(Collectors.toList());

            // 图片集合
            List<String> referImageList = new ArrayList<>(8);
            // 获取主图列表图片和spu附图列表图片
            if (StringUtils.isNotBlank(spu)) {
                // 优先取，SKU-effect，SKU-cmb
                referImageList.addAll(newImages.stream()
                        .filter(image -> image.contains(sku + "-cmb") || image.contains(sku + "-effect"))
                        .sorted((image1, image2) -> {
                            if (image1.contains(sku + "-effect") && !image2.contains(sku + "-effect")) {
                                return -1;
                            } else if (!image1.contains(sku + "-effect") && image2.contains(sku + "-effect")) {
                                return 1;
                            } else {
                                return 0;
                            }
                        })
                        .collect(Collectors.toList()));
                // 取SPU-effect，SPU-cmb
                if (referImageList.isEmpty()) {
                    String cmbPattern = String.format("/%s-cmb", spu);
                    String effectPattern = String.format("/%s-effect", spu);
                    List<String> collect = images.stream()
                            .filter(image -> image.contains(cmbPattern) || image.contains(effectPattern))
                            .sorted((image1, image2) -> {
                                if (image1.contains(effectPattern) && !image2.contains(effectPattern)) {
                                    return -1;
                                } else if (!image1.contains(effectPattern) && image2.contains(effectPattern)) {
                                    return 1;
                                } else {
                                    return 0;
                                }
                            })
                            .collect(Collectors.toList());
                    referImageList.addAll(collect);
                }
            }

            // 过滤已经选择的图片然后随机取newImages中的图片
            Collections.shuffle(newImages, new Random());

            List<String> randomImages = newImages.stream()
                    .filter(image -> !referImageList.contains(image) && !image.contains("-cmb") && !image.contains("-effect"))
                    .collect(Collectors.toList());
            referImageList.addAll(randomImages);

            // 若超过8张图片，则截取前8张
            if (referImageList.size() > 8) {
                return referImageList.subList(0, 8);
            }
            return referImageList;
        }
        // 单体照片
        List<String> singleImageList = images.stream()
                .filter(image -> image.contains(spu + "-cmb") || image.contains(spu + "-effect"))
                .sorted((image1, image2) -> {
                    if (image1.contains(sku + "-effect") && !image2.contains(sku + "-effect")) {
                        return -1;
                    } else if (!image1.contains(sku + "-effect") && image2.contains(sku + "-effect")) {
                        return 1;
                    } else {
                        return 0;
                    }
                })
                .collect(Collectors.toList());

        // 打乱图片顺序
        Collections.shuffle(images, new Random());
        List<String> filterSingleImages = images.stream()
                .filter(image -> !singleImageList.contains(image) && !image.contains("-cmb") && !image.contains("-effect"))
                .collect(Collectors.toList());

        // 选择前八个图片
        List<String> singleImages = filterSingleImages.subList(0, Math.min(8 - singleImageList.size(), filterSingleImages.size()));
        singleImageList.addAll(singleImages);
        return singleImageList;

    }

    /**
     * 获取sku同名规则图片,需要随机
     *
     * @param articleNumber articleNumber
     * @param images        产品图片池
     * @return 目标图片
     */
    public static String getSkuMainImage(String articleNumber, List<String> images) {
        if (CollectionUtils.isEmpty(images)) {
            return null;
        }

        // 随机取SKU命名相同的图，子SKU命名 -00 ，-000命名的图片
        List<String> mainImageList = images.stream()
                .filter(image -> image.contains(String.format("/%s.", articleNumber))
                        || image.contains(String.format("/%s.", articleNumber + "-00"))
                        || image.contains(String.format("/%s.", articleNumber + "-000"))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainImageList)) {
            return null;
        }
        Collections.shuffle(mainImageList);
        return mainImageList.get(0);

    }

    /**
     * 匹配标题、描述、五点描述
     */
    public static void matchingTemplateInfoNew(String mainSku, WalmartTemplateDTO templateDTO) {
        SpuOfficial amazonSpuOfficial = CommonMatchProdInfoUtil.matchSpuOfficial(mainSku);
        // 获取标题描述五点描述
        TitleDescription titleDescription = WalmartCommonUtils.matchingAmazonSpuTitleDescription(amazonSpuOfficial);
        if (titleDescription == null) {
            SpuOfficial spuOfficial = getSpuOfficial(mainSku);
            titleDescription = WalmartCommonUtils.matchingSpuTitleDescription(spuOfficial, true);
            if (titleDescription == null) {
                titleDescription = WalmartCommonUtils.matchingTitleDescription(spuOfficial, amazonSpuOfficial);
            }
        }
        templateDTO.setTitle(titleDescription.getTitle());
        templateDTO.setDescription(titleDescription.getDescription());
        templateDTO.setKeyFeatures(titleDescription.getKeyFeatures());

        List<WalmartVariant> walmartVariantList = templateDTO.getWalmartVariantList();

        String attributeStr = getAttributeStr(walmartVariantList);

        // 校验侵权词
        Map<String, String> infringingWords = checkInfringementWords(titleDescription.getTitle(), titleDescription.getDescription(), null, titleDescription.getKeyFeatures(), attributeStr);
        if (MapUtils.isNotEmpty(infringingWords)) {
            templateDTO.setInfringingWords(infringingWords);
        }
    }


    /**
     * 获取属性信息p拼接
     *
     * @param variants
     * @return
     */
    public static String getAttributeStr(List<WalmartVariant> variants) {
        if (CollectionUtils.isNotEmpty(variants)) {
            return variants.stream()
                    .filter(variant -> MapUtils.isNotEmpty(variant.getAttributeMap()))
                    .map(variant -> variant.getAttributeMap().values().stream()
                            .filter(Objects::nonNull).map(String::valueOf)
                            .collect(Collectors.joining(StrConstant.CHECK_INFRING_WORD_SPLIT))
                    ).collect(Collectors.joining(StrConstant.CHECK_INFRING_WORD_SPLIT));
        }
        return null;
    }


}
