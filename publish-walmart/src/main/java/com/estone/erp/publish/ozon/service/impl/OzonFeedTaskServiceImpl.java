package com.estone.erp.publish.ozon.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.C<PERSON>uery;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItemOffline;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateDO;
import com.estone.erp.publish.ozon.model.dto.solution.ErrorSolutionResult;
import com.estone.erp.publish.ozon.model.dto.solution.SolutionItem;
import com.estone.erp.publish.ozon.model.vo.OzonActionItemVo;
import com.estone.erp.publish.ozon.model.vo.OzonFailReportVO;
import com.estone.erp.publish.ozon.mq.OzonMqConfig;
import com.estone.erp.publish.ozon.service.OzonErrorSolutionService;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.ozon.service.OzonProblemClassificationHandler;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.platform.service.SmallPlatformExcelDownloadLogService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskCriteria;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-05-09 17:34
 */
@Slf4j
@Service
public class OzonFeedTaskServiceImpl implements OzonFeedTaskService {

    @Autowired
    private FeedTaskService feedTaskService;

    @Resource
    private PermissionsHelper permissionsHelper;

    @Autowired
    private OzonProblemClassificationHandler problemClassificationHandler;

    @Resource
    private OzonErrorSolutionService ozonErrorSolutionService;

    @Override
    public FeedTask newMarketingTask(boolean success, String msg, Long businessId, String accountNumber, String taskType, String marketingName, Integer marketingId, Integer marketingTaskId, OzonActionItemVo ozonActionItemVo) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(businessId != null ? String.valueOf(businessId) : null);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTableIndex();
        // attribute6 是用于规则名称，其他不要动用
        feedTask.setAttribute6(marketingName);
        feedTask.setAttribute7(marketingId != null ? String.valueOf(marketingId) : null);
        feedTask.setAttribute8(marketingTaskId != null ? String.valueOf(marketingTaskId) : null);
        feedTask.setAttribute9(ozonActionItemVo != null ? JSON.toJSONString(ozonActionItemVo) : null);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setResultStatus(success ? FeedTaskResultStatusEnum.SUCCESS.getResultStatus() : FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setResultMsg(msg);
        if (ozonActionItemVo != null) {
            String sku = ozonActionItemVo.getSku();
            String sellerSku = ozonActionItemVo.getSellerSku();
            feedTask.setArticleNumber(sku);
            feedTask.setAttribute1(sellerSku);
        }
        if (!success) {
            problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, msg, taskType);
        }
        feedTaskService.insertSelective(feedTask);
        return feedTask;
    }

    @Override
    public FeedTask createTask(boolean success, String msg, Long businessId, String accountNumber, String taskType, OzonActionItemVo ozonActionItemVo, Timestamp createTime, Timestamp finishTime) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(businessId != null ? String.valueOf(businessId) : null);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(createTime);
        feedTask.setFinishTime(finishTime);
        feedTask.setTableIndex();
        // attribute6 是用于规则名称，其他不要动用
        feedTask.setAttribute9(ozonActionItemVo != null ? JSON.toJSONString(ozonActionItemVo) : null);
        feedTask.setResultStatus(success ? FeedTaskResultStatusEnum.SUCCESS.getResultStatus() : FeedTaskResultStatusEnum.FAIL.getResultStatus());
        if (ozonActionItemVo != null) {
            String sku = ozonActionItemVo.getSku();
            String sellerSku = ozonActionItemVo.getSellerSku();
            feedTask.setArticleNumber(sku);
            feedTask.setAttribute1(sellerSku);
        }
        feedTask.setResultMsg(msg);
        if (!success) {
            problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, msg, taskType);
        }
        return feedTask;
    }

    /**
     * 初始一条处理报告
     */
    @Override
    @Transactional(readOnly = false)
    public FeedTask newTask(Long productId, String accountNumber, String taskType, String sku, String... s) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(productId!=null?String.valueOf(productId):null);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTableIndex();
        getFeed(feedTask, s);
        feedTaskService.insertSelective(feedTask);
        return feedTask;
    }
    @Override
    @Transactional(readOnly = false)
    public FeedTask initPublishFeedTask(Integer excelId, Integer rowIndex, String ruleName, Long productId, String accountNumber, String taskType, String sku, String... s) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(productId!=null?String.valueOf(productId):null);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setAttribute6(ruleName);
        feedTask.setTableIndex();
        feedTask.setAttribute7(Optional.ofNullable(excelId).map(Object::toString).orElse(""));
        feedTask.setAttribute8(Optional.ofNullable(rowIndex).map(Object::toString).orElse(""));
        getFeed(feedTask, s);
        feedTaskService.insertSelective(feedTask);
        return feedTask;
    }
    /**
     * 初始一条处理报告
     */
    @Override
    @Transactional(readOnly = false)
    public FeedTask newWaitingTask(Long productId, String accountNumber, String taskType, String sku, String... s) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(productId!=null?String.valueOf(productId):null);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.WAITING.getTaskStatus());
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTableIndex();
        getFeed(feedTask, s);
        feedTaskService.insertSelective(feedTask);
        return feedTask;
    }

    /**
     * 成功
     *
     * @param feedTask
     * @param msg
     */
    @Override
    @Transactional(readOnly = false)
    public void succeedTask(FeedTask feedTask, String msg, String... s) {
        feedTask.setResultMsg(msg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTableIndex();
        feedTaskService.updateByPrimaryKeySelective(feedTask);
//        return feedTask;
    }

    /**
     * 任务还在执行中，没有成功和失败
     * @param feedTask 任务
     * @param msg 消息
     */
    @Override
    public void runningTask(FeedTask feedTask, String msg) {
        feedTask.setResultMsg(msg);
        feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTableIndex();
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    /**
     * 失败
     *
     * @param
     * @param msg
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void failTask(FeedTask feedTask, String msg, String... s) {
        if (feedTask == null) {
            return;
        }

        feedTask.setResultMsg(msg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTableIndex();
        
        // 进行问题分类分析 - 针对所有失败的情况
        try {
            if (StringUtils.isNotBlank(msg)) {
                String operationType = StringUtils.isNotBlank(feedTask.getTaskType()) ? feedTask.getTaskType() : "UNKNOWN";
                problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, msg, operationType);
            }
        } catch (Exception e) {
            log.error("FeedTask[{}]问题分类处理失败: {}", feedTask.getId(), e.getMessage(), e);
        }
        
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }


    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void batchNewUpdateFeeds(List<OzonUpdateDO> ozonUpdateDOS, String taskType) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (OzonUpdateDO ozonUpdateDO : ozonUpdateDOS) {
            FeedTask feedTask = new FeedTask();
            feedTask.setAssociationId(String.valueOf(ozonUpdateDO.getProductId()));
            feedTask.setAccountNumber(ozonUpdateDO.getAccountNumber());
            feedTask.setArticleNumber(ozonUpdateDO.getSku());
            feedTask.setTaskType(taskType);
            feedTask.setPlatform(Platform.Ozon.name());
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTask.setCreatedBy(StringUtils.defaultString(StringUtils.isBlank(WebUtils.getUserName())?DataContextHolder.getUsername():WebUtils.getUserName(),"admin"));
            feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setAttribute9(ozonUpdateDO.getStockObj() != null ? JSON.toJSONString(ozonUpdateDO.getStockObj()) : null);
            feedTask.setAttribute10(ozonUpdateDO.getJob());
            String before = OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name().equals(taskType) ? String.valueOf(ozonUpdateDO.getUpdateBeforeStock() ) : ozonUpdateDO.getUpdateBeforePrice();
            String after = OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name().equals(taskType) ? String.valueOf(ozonUpdateDO.getUpdateAfterStock() ) : ozonUpdateDO.getUpdateAfterPrice();
            if (OzonFeedTaskEnums.TaskType.UPDATE_PRICE.name().equals(taskType)) {
                after = StringUtils.defaultString(ozonUpdateDO.getUpdateAfterOriginPrice(),"0")+", "+after;
            }
            String warehouseId = ozonUpdateDO.getWarehouseId() != null ? ozonUpdateDO.getWarehouseId().toString() : null;
            getFeed(feedTask, new String[]{ozonUpdateDO.getSellerSku(), ozonUpdateDO.getSku(), before, after, warehouseId});
            feedTask.setResultMsg(ozonUpdateDO.getWarehouseName());
            feedTask.setTableIndex();
            feedTasks.add(feedTask);
        }
        feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
    }

    /**
     * 该方法需要自己手动去添加解析出题处理报告
     * @param feedTasks
     */
    @Override
    public void batchInsertSelective(List<FeedTask> feedTasks) {
        if (CollectionUtils.isEmpty(feedTasks)) {
            return;
        }
        feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void batchUpdateFail(String accountNumber, List<OzonUpdateDO> ozonUpdateDOS, String taskType, String errorMsg) {
        List<String> associationIds = ozonUpdateDOS.stream().map(OzonUpdateDO::getProductId).map(String::valueOf).collect(Collectors.toList());
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = feedTaskExample.createCriteria();
        criteria.andAccountNumberEqualTo(accountNumber);
        criteria.andTaskStatusEqualTo(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        criteria.andTaskTypeEqualTo(taskType);
        criteria.andAssociationIdIn(associationIds);
        List<FeedTask> feedTasks = feedTaskService.selectByExample(feedTaskExample, Platform.Ozon.name());
        if (CollectionUtils.isNotEmpty(feedTasks)) {
            for (FeedTask feedTask : feedTasks) {
                feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                feedTask.setResultMsg(StringUtils.defaultString(feedTask.getResultMsg(),"") + errorMsg);
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                feedTask.setTableIndex();
                problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, errorMsg, taskType);
            }
            feedTaskService.batchUpdateFeedTask(feedTasks);
        }
    }

    @Override
    public List<FeedTask> selectByExample(FeedTaskExample feedTaskExample, String name) {
        return feedTaskService.selectByExample(feedTaskExample, name);
    }

    /**
     * 问题分类信息需要外部进行处理
     * @param feedTasks
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void batchUpdateFeeds(List<FeedTask> feedTasks) {
        if (CollectionUtils.isEmpty(feedTasks)) {
            return;
        }
        for (FeedTask feedTask : feedTasks) {
            feedTask.setPlatform(Platform.Ozon.name());
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            feedTask.setTableIndex();
        }
        List<List<FeedTask>> lists = PagingUtils.newPagingList(feedTasks, 100);
        for (List<FeedTask> list : lists) {
            feedTaskService.batchUpdateFeedTask(list);
        }
    }

    @Override
    public void batchUpdateTaskIdAndRunning(List<FeedTask> feedTasks) {
        if (CollectionUtils.isEmpty(feedTasks)) {
            return;
        }
        for (FeedTask feedTask : feedTasks) {
            feedTask.setPlatform(Platform.Ozon.name());
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTask.setTableIndex();
        }
        List<List<FeedTask>> lists = PagingUtils.newPagingList(feedTasks, 100);
        for (List<FeedTask> list : lists) {
            feedTaskService.batchUpdateFeedTask(list);
        }
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void batchNewItemFeeds(List<EsOzonItem> itemList, String taskType) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (EsOzonItem item : itemList) {
            FeedTask feedTask = new FeedTask();
            feedTask.setAssociationId(String.valueOf(item.getProductId()));
            feedTask.setAccountNumber(item.getAccountNumber());
            feedTask.setArticleNumber(item.getSku());
            feedTask.setTaskType(taskType);
            feedTask.setPlatform(Platform.Ozon.name());
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTask.setCreatedBy(StringUtils.isBlank(WebUtils.getUserName())?DataContextHolder.getUsername():WebUtils.getUserName());
            feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
            getFeed(feedTask, new String[]{item.getSellerSku(), item.getSku()});
            feedTask.setTableIndex();
            feedTasks.add(feedTask);
        }
        feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void batchOfflineItemFeeds(List<EsOzonItemOffline> itemList, String taskType) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (EsOzonItemOffline item : itemList) {
            FeedTask feedTask = new FeedTask();
            feedTask.setAssociationId(String.valueOf(item.getProductId()));
            feedTask.setAccountNumber(item.getAccountNumber());
            feedTask.setArticleNumber(item.getSku());
            feedTask.setTaskType(taskType);
            feedTask.setPlatform(Platform.Ozon.name());
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTask.setCreatedBy(StringUtils.isBlank(WebUtils.getUserName())?DataContextHolder.getUsername():WebUtils.getUserName());
            feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
            getFeed(feedTask, new String[]{item.getSellerSku(), item.getSku()});
            feedTask.setTableIndex();
            feedTasks.add(feedTask);
        }
        feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
    }

    @Override
    public void newPublishFailFeedTask(String templateId, String accountNumber, String articleNumber, String message,
                                       String sellerSku, String ruleName, Integer excelId, Integer rowIndex) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(StringUtils.defaultString(templateId != null ? String.valueOf(templateId): null, ""));
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(articleNumber);
        feedTask.setTaskType(OzonFeedTaskEnums.TaskType.PUBLISH.name());
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTableIndex();
        feedTask.setResultMsg(message);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setAttribute1(sellerSku);
        feedTask.setAttribute6(ruleName);
        feedTask.setAttribute7(Optional.ofNullable(excelId).map(Object::toString).orElse(""));
        feedTask.setAttribute8(Optional.ofNullable(rowIndex).map(Object::toString).orElse(""));
        problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, message, OzonFeedTaskEnums.TaskType.PUBLISH.name());

        feedTaskService.insertSelective(feedTask);
    }

    @Override
    public void updatePublishFail(Long feedTaskId, Integer templateId, String accountnumber, String message) {
        // 兼容以前的数据，处理报告太多了，还是得带上feedTaskId
        if (feedTaskId == null) {
            FeedTaskExample feedTaskExample = new FeedTaskExample();
            feedTaskExample.createCriteria()
                    .andAssociationIdEqualTo(String.valueOf(templateId))
                    .andAccountNumberEqualTo(accountnumber)
                    .andTaskTypeEqualTo(OzonFeedTaskEnums.TaskType.PUBLISH.name())
                    .andResultStatusEqualTo(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
            FeedTask feedTask = new FeedTask();
            feedTask.setPlatform(Platform.Ozon.name());
            feedTask.setResultMsg(message);
            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
            feedTask.setTableIndex();
            feedTask.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));
            problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, message, OzonFeedTaskEnums.TaskType.PUBLISH.name());
            feedTaskService.updateByExampleSelective(feedTask,feedTaskExample);
        } else {
            FeedTask feedTask = selectById(feedTaskId);
            feedTask.setPlatform(Platform.Ozon.name());
            feedTask.setResultMsg(message);
            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
            feedTask.setTableIndex();
            feedTask.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));
            feedTask.setId(feedTaskId);
            problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, message, OzonFeedTaskEnums.TaskType.PUBLISH.name());

            feedTaskService.updateByPrimaryKeySelective(feedTask);
        }
    }

    @Override
    public void updateFail(Long feedTaskId, String message, String taskType) {
        if (feedTaskId == null) {
            return;
        }
        FeedTask feedTask = new FeedTask();
        feedTask.setResultMsg(message);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setId(feedTaskId);
        feedTask.setTableIndex();
        problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, message, taskType);
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    @Override
    public void updateSuccess(Long feedTaskId, String message) {
        if (feedTaskId == null) {
            return;
        }
        FeedTask feedTask = new FeedTask();
        feedTask.setResultMsg(message);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setId(feedTaskId);
        feedTask.setTableIndex();
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    @Override
    public void updateUpdateItemFailTask(String accountNumber, Long taskId, String message, String taskType) {
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        feedTaskExample.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andTaskTypeEqualTo(taskType)
                .andResultStatusEqualTo(FeedTaskResultStatusEnum.SUCCESS.getResultStatus())
                .andAttribute2EqualTo(taskId.toString());

        FeedTask feedTask = new FeedTask();
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setResultMsg(message);
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
        feedTask.setTableIndex();
        feedTask.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));

        problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, message, taskType);
        
        feedTaskService.updateByExampleSelective(feedTask,feedTaskExample);
    }

    @Override
    public ApiResult<List<OzonFailReportVO>> getPublishFailReport(String templateId) {
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        feedTaskExample.createCriteria()
                .andAssociationIdEqualTo(templateId)
                .andTaskTypeEqualTo(OzonFeedTaskEnums.TaskType.PUBLISH.name())
                .andResultStatusEqualTo(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTaskExample.setOrderByClause("id desc");
        feedTaskExample.setCustomColumn("id, task_type, attribute1, result_msg");

        List<FeedTask> feedTaskList = feedTaskService.selectByExample(feedTaskExample, Platform.Ozon.name());
        if (CollectionUtils.isEmpty(feedTaskList)) {
            return ApiResult.newError("未查询到失败处理报告");
        }

        List<OzonFailReportVO> failReportVOS = feedTaskList.stream().map(feedTask -> {
            String sellerSku = feedTask.getAttribute1();
            OzonFailReportVO failReportVO = new OzonFailReportVO();
            failReportVO.setSellerSku(sellerSku);
            failReportVO.setMessage(feedTask.getResultMsg());
            ErrorSolutionResult solutionForError = ozonErrorSolutionService.findSolutionForError(feedTask.getResultMsg(), feedTask.getTaskType());
            if (solutionForError != null && solutionForError.isSuccess()) {
                List<SolutionItem> solutions = solutionForError.getSolutions();
                failReportVO.setSolutions(solutions);
            }

            return failReportVO;
        }).collect(Collectors.toList());
        return ApiResult.newSuccess(failReportVOS);
    }

    @Override
    public FeedTask getExecutingSyncItemTask(String accountNumber, List<String> syncTypeList) {
        // 半天前
        LocalDateTime beginTime = LocalDateTime.now().minusHours(48);
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        feedTaskExample.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andArticleNumberIn(syncTypeList)
                .andCreatedByGreaterThan(beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .andTaskTypeEqualTo(OzonFeedTaskEnums.TaskType.SYNC_ITEM.name())
                .andTaskStatusEqualTo(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        feedTaskExample.setLimit(1);
        List<FeedTask> feedTaskList = feedTaskService.selectByExample(feedTaskExample, Platform.Ozon.name());
        if (CollectionUtils.isEmpty(feedTaskList)) {
            return null;
        }
        return feedTaskList.get(0);
    }

    @Override
    public void addFailFeedTask(Long productId, String accountNumber, String taskType, String sku, String failMsg, String... s) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(productId!=null?String.valueOf(productId):null);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setResultMsg(failMsg);
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        feedTask.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));
        feedTask.setTableIndex();
        getFeed(feedTask, s);
        problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, failMsg, taskType);
        feedTaskService.insertSelective(feedTask);
    }

    /**
     * 不进行问题处理报告维护
     * @param feedTask
     * @param status
     */
    @Override
    public void updateTaskStatus(FeedTask feedTask, Integer status) {
        feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(status);
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTableIndex();
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    /**
     * 处理拓展字段
     *
     * @param feedTask
     * @param s Attribute1：sellerSku
     *                 Attribute2：sku
     *                 Attribute3：改前值
     *                 Attribute4：改后值
     *                 Attribute5：模板id
     *
     */
    public void getFeed(FeedTask feedTask, String[] s) {
        for (int i = 0; i < s.length; i++) {
            switch (i) {
                case 0:
                    feedTask.setAttribute1(s[0]);
                    break;
                case 1:
                    feedTask.setAttribute2(s[1]);
                    break;
                case 2:
                    feedTask.setAttribute3(s[2]);
                    break;
                case 3:
                    feedTask.setAttribute4(s[3]);
                    break;
                case 4:
                    feedTask.setAttribute5(s[4]);
                    break;
            }
        }
    }

    @Override
    public boolean existExecutingTask(String accountNumber, String taskType, Integer day) {
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = feedTaskExample.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andTaskTypeEqualTo(taskType)
                .andTaskStatusEqualTo(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        // 最近12小时同步过的，不允许再同步
        LocalDateTime localDateTime = LocalDateTime.now().minusHours(12);
        criteria.andCreateTimeGreaterThan(Timestamp.valueOf(localDateTime));
        int i = feedTaskService.countByExample(feedTaskExample, Platform.Ozon.name());
        return i > 0;
    }

    @Autowired
    private SmallPlatformExcelDownloadLogService downloadLogService;

    @Autowired
    private RabbitMqSender rabbitMqSender;

    @Override
    public Integer exportFeedTasks(CQuery<FeedTaskCriteria> queryCondition) {
        FeedTaskCriteria criteria = queryCondition.getSearch();
        criteria.setPlatform(SaleChannel.CHANNEL_OZON);

        String accountNumber = criteria.getAccountNumber();
        String accountNumbers = criteria.getAccounts();
        String taskType = criteria.getTaskType();
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_OZON);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new BusinessException(superAdminOrEquivalent.getErrorMsg());
        }

        if (WalmartTaskTypeEnum.DOANLOAD_IMAGE.getStatusMsgEn().equalsIgnoreCase(taskType)) {
            String platform = SaleChannel.CHANNEL_WALMART;
            // 人员权限
            Pair<Boolean, List<String>> employeePair = PermissionsHelper.getDefaultOrAuthorEmployeePair(platform, criteria.getCreatedBy(), criteria.getCreateByList(),
                    accountNumber, CommonUtils.splitList(accountNumbers, ","));
            if (BooleanUtils.isTrue(employeePair.getLeft())) {
                criteria.setCreateByList(employeePair.getRight());
            }
        } else if (!superAdminOrEquivalent.getResult()) {
            List<String> accounts = CommonUtils.splitList(accountNumbers, ",");
            List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(accounts, null, null, null, SaleChannel.CHANNEL_OZON, true);
            criteria.setAccounts(StringUtils.join(currentUserPermission, ","));
        }

        // Create download log
        SmallPlatformExcelDownloadLog downloadLog = new SmallPlatformExcelDownloadLog();
        downloadLog.setQueryCondition(JSON.toJSONString(criteria));
        downloadLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
        downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
        downloadLog.setType(SmallPlatformDownloadEnums.Type.FEED_TASK.name());
        downloadLog.setStatus(SmallPlatformDownloadEnums.Status.WAIT.getCode());
        downloadLog.setPlatform(SaleChannel.CHANNEL_OZON);

        // Insert download log
        downloadLogService.insert(downloadLog);

        // Send to MQ
        rabbitMqSender.allPublishVHostRabbitTemplateSend(
                OzonMqConfig.OZON_API_DIRECT_EXCHANGE,
                OzonMqConfig.OZON_DOWNLOAD_QUEUE_KEY,
                downloadLog.getId());

        return downloadLog.getId();
    }


    @Override
    public Integer exportPublishFeedTasks(CQuery<FeedTaskCriteria> queryCondition) {
        FeedTaskCriteria criteria = queryCondition.getSearch();
        criteria.setPlatform(SaleChannel.CHANNEL_OZON);

        String accountNumbers = criteria.getAccounts();
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_OZON);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new BusinessException(superAdminOrEquivalent.getErrorMsg());
        }

        if (!superAdminOrEquivalent.getResult()) {
            List<String> accounts = CommonUtils.splitList(accountNumbers, ",");
            List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(accounts, null, null, null, SaleChannel.CHANNEL_OZON, true);
            criteria.setAccounts(StringUtils.join(currentUserPermission, ","));
        }
        criteria.setAssociationIdIsNotNull(true);
        criteria.setTaskType(OzonFeedTaskEnums.TaskType.PUBLISH.name());

        FeedTaskExample example = criteria.getExample();
        int i = feedTaskService.countByExample(example, Platform.Ozon.name());
        if (i > 500000) {
            throw new RuntimeException("一次最多导出50W条数据");
        }

        // Create download log
        SmallPlatformExcelDownloadLog downloadLog = new SmallPlatformExcelDownloadLog();
        downloadLog.setQueryCondition(JSON.toJSONString(criteria));
        downloadLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
        downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
        downloadLog.setType(SmallPlatformDownloadEnums.Type.FEED_TASK_PUBLISH_TEMPLATE.name());
        downloadLog.setStatus(SmallPlatformDownloadEnums.Status.WAIT.getCode());
        downloadLog.setPlatform(SaleChannel.CHANNEL_OZON);

        // Insert download log
        downloadLogService.insert(downloadLog);

        // Send to MQ
        rabbitMqSender.allPublishVHostRabbitTemplateSend(
                OzonMqConfig.OZON_API_DIRECT_EXCHANGE,
                OzonMqConfig.OZON_DOWNLOAD_QUEUE_KEY,
                downloadLog.getId());

        return downloadLog.getId();
    }

    @Override
    public void insert(FeedTask feedTask) {
        feedTaskService.insert(feedTask);
    }

    @Override
    public int batchUpdateFeedTaskToFinish(List<FeedTask> feedTasks, int taskStatus, int resultStatus, String msg) {
        return feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, taskStatus, resultStatus, msg);
    }

    @Override
    public FeedTask selectById(Long id) {
        return feedTaskService.selectByPrimaryKey(id, SaleChannel.CHANNEL_OZON);
    }
}
