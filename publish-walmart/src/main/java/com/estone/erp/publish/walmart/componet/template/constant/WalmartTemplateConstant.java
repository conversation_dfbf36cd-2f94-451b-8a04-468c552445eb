package com.estone.erp.publish.walmart.componet.template.constant;

/**
 * walmart模板常量类
 *
 * <AUTHOR>
 * @date 2025年8月4日
 */
public class WalmartTemplateConstant {

    /**
     * 默认刊登分类id
     */
    public static final String DEFAULT_CATEGORY_ID = "304780077";
    public static final String DEFAULT_CATEGORY_NAME = "Hardware Washers";


    /**
     * 上面分类的必填属性,默认刊登分类属性
     */
    public static final String DEFAULT_CATEGORY_ATTRIBUTE = "{\"condition\":\"New\",\"has_written_warranty\":\"No\",\"isProp65WarningRequired\":\"No\",\"netContent\":{\"productNetContentMeasure\":1,\"productNetContentUnit\":\"Count\"},\"smallPartsWarnings\":[\"3 - Choking hazard contains small parts\"]}";


    /**
     * 变体主题
     */
    public static final String DEFAULT_VARIATION_THEME = "color";
}
