package com.estone.erp.test.walmart;

/**
 * @Auther yucm
 * @Date 2020/11/21
 */

import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.walmart.call.authentication.WalmartGetTokenCall;
import org.junit.Test;

import java.sql.Timestamp;
import java.time.LocalDateTime;

public class WalmartAccountTest {

    @Test
    public void testWalmartGetTokenCall() {
        SaleAccountAndBusinessResponse walmartPmsAccount = new SaleAccountAndBusinessResponse();
        walmartPmsAccount.setClientId(TestConstants.CLIENT_ID);
        walmartPmsAccount.setClientSecret(TestConstants.CLIENT_SECRET);

        String accessToken = new WalmartGetTokenCall(walmartPmsAccount).execute();
        System.out.println(accessToken);
    }

    @Test
    public void testWalmartGetTokenCall2() {
        Timestamp createDate = Timestamp.valueOf(LocalDateTime.of(2025, 6, 1, 0, 0, 0));
        Timestamp localDateTime = Timestamp.valueOf(LocalDateTime.of(2025, 5, 31, 0, 0, 0));
        // 上架时间是否在30天前
        System.out.println(localDateTime.before(createDate));
    }
}