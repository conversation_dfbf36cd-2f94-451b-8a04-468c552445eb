package com.estone.erp.publish.amazon.util;

import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.*;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.submit.FeedType;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.service.AmazonParentRelationshipService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.CheckOrderSalesTimeUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.model.listings.ListingsItemSubmissionResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.data.domain.Page;

import java.util.*;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;


@Slf4j
public class DeleteAmazonListingUtils {

    // 下架需要查询的字段
    public static final String AMAZON_PRODUCT_LISTING_COLUMNS="id,accountNumber,site,parentAsin, sonAsin, sellerSku,mainSku,articleNumber,itemStatus,isOnline,itemType,skuStatus,skuDataSource,categoryId,attribute3";
    // 下架需要查询的es字段
    public static final String []  ES_AMAZON_PRODUCT_LISTING_FIELDS={"id","accountNumber","site","parentAsin", "sonAsin",
            "sellerSku", "mainSku", "articleNumber", "itemStatus", "isOnline", "itemType", "skuStatus", "skuDataSource",
            "categoryId", "order_num_total", "normalSale", "openDate", "firstOpenDate", "specialGoodsCode"};

    public static final List<String> BAD_ASINS = Arrays.asList("Inactive","Incomplete");

    public static final List<Integer> ITEM_TYPE_LIST = List.of(AmazonListingitemtypeEnum.Vriant_Item.getStatusCode(), AmazonListingitemtypeEnum.Monomer_Item.getStatusCode());

    public static final String NOT_ARTICLENUMER  = "匹配不到货号";

    public static final String SELF_REGISTERD_SKU_SITE = "US";

    public static List<String> selfRegisteredSkuList = new ArrayList<>();
    public static Long staticSelfRegisteredSkuExpirationTime = 0L;
    private static Semaphore ACCOUNT_SEMAPHORE = new Semaphore(10);

    private static AmazonProductListingService amazonProductListingService = SpringUtils.getBean(AmazonProductListingService.class);
    private static EsAmazonProductListingService esAmazonProductListingService = SpringUtils.getBean(EsAmazonProductListingService.class);
    private static  AmazonProcessReportService amazonProcessReportService = SpringUtils.getBean(AmazonProcessReportService.class);
    private static  SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
    private static AmazonParentRelationshipService amazonParentRelationshipService = SpringUtils.getBean(AmazonParentRelationshipService.class);

    /**
     * 页面调用下架,批量下架产品-单个执行 不用多线程，重试 3次
     * @param amazonProductListingList 需要包含：账号、站点、子asin、父asin、sellerSku、itemType、货号
     * @param biConsumer
     * @param deleteAmazonListingDto
     * @return
     */
    public static String batchRetireProductSingle(List<AmazonProductListing> amazonProductListingList,
                                                  BiConsumer<AmazonProductListing, ApiResult<ListingsItemSubmissionResponse>> biConsumer, DeleteAmazonListingDto deleteAmazonListingDto) {
        String uName = WebUtils.getUserName();
        if (StringUtils.isBlank(uName)) {
            uName = DataContextHolder.getUsername();
            if (org.apache.commons.lang.StringUtils.isBlank(uName)) {
                return "用户登录失效！";
            }
        }
        String userName = uName;
        deleteAmazonListingDto.setUserName(userName);
        for (AmazonProductListing amazonProductListing : amazonProductListingList) {
            AmazonExecutors.executeSaleAmazonDeleteListing(() -> {
                offlineTask(amazonProductListing,  biConsumer,null,deleteAmazonListingDto);
            });
        }
        return null;
    }

    /**
     *  页面调用下架
     * @param amazonProductListingList 需要包含：账号、站点、子asin、父asin、sellerSku、itemType、货号
     * @param deleteAmazonListingDto
     * @param biConsumer
     * @return
     */
    public static String batchRetireProduct(List<AmazonProductListing> amazonProductListingList,
                                            BiConsumer<AmazonProductListing, ApiResult<ListingsItemSubmissionResponse>> biConsumer, DeleteAmazonListingDto deleteAmazonListingDto) {
        String uName = WebUtils.getUserName();
        if (StringUtils.isBlank(uName)) {
            uName = DataContextHolder.getUsername();
            if (org.apache.commons.lang.StringUtils.isBlank(uName)) {
                return "用户登录失效！";
            }
        }
        String userName = uName;
        deleteAmazonListingDto.setUserName(userName);
        String deleteRemark = deleteAmazonListingDto.getDeleteRemark();

        for (AmazonProductListing amazonProductListing : amazonProductListingList) {
            amazonProductListing.setAttribute3(deleteRemark);
            AmazonExecutors.executeSaleAmazonDeleteListing(() -> {
                offlineTask(amazonProductListing,  biConsumer,null,deleteAmazonListingDto);
            });
        }

        return null;
    }


    /**
     * 系统下架
     * @param amazonProductListingList 需要包含：账号、站点、子asin、父asin、sellerSku、itemType、货号
     * @param deleteAmazonListingDto
     * @param biConsumer
     */
    public static void systemBatchRetireProduct(List<AmazonProductListing> amazonProductListingList, DeleteAmazonListingDto deleteAmazonListingDto,
                                                BiConsumer<AmazonProductListing, ApiResult<ListingsItemSubmissionResponse>> biConsumer) {
        String userName = "admin";
        deleteAmazonListingDto.setUserName(userName);

        AmazonExecutors.executeSystemAmazonDeleteListing(() -> {
            Map<String, List<AmazonProductListing>> accountNumberAmazonProductListingsMap = amazonProductListingList.stream().collect(Collectors.groupingBy(o -> o.getAccountNumber()));
            EsSaleAccountRequest request = new EsSaleAccountRequest();
            request.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
            request.setAccountNumberList(new ArrayList<>(accountNumberAmazonProductListingsMap.keySet()));
            List<SaleAccount> saleAccountList = saleAccountService.getAccountInfoList(request, "accountNumber", "merchantId");

            // 按照merchantId 分组 同一个merchantId为套账 共用一个限流
            Map<String, List<String>> merchantIdAccountsMap = saleAccountList.stream()
                    .filter(account -> (StringUtils.isNotBlank(account.getAccountNumber()) && StringUtils.isNotBlank(account.getMerchantId())))
                    .collect(Collectors.groupingBy(SaleAccount::getMerchantId, Collectors.mapping(SaleAccount::getAccountNumber, Collectors.toList())));
            merchantIdAccountsMap.forEach((merchantId, accountNumbers) -> {
                // 同一套账,同一时间只允许一个账号执行下架
                for (String accountNumber : accountNumbers) {
                    try {
                        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
                        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
                        // 过滤未授权到sp-api的账号
                        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
                            log.error("下架不执行，店铺未授权到sp-api,账号信息不全,请检查 marketplaceId，appName 该 accountNumber:" + accountNumber);
                            continue;
                        }
                        // 排除订单FBA库存管理存在的asin
                        List<AmazonProductListing> amazonProductListings = accountNumberAmazonProductListingsMap.get(accountNumber);
                        execDeleteListings(merchantId, accountNumber, amazonProductListings, biConsumer, amazonSpAccount, deleteAmazonListingDto);
                    } catch (Exception e) {
                        log.error("账号：{},商家ID:{},按merchantId下架链接异常：{}", accountNumber, merchantId, e.getMessage());
                    }
                }
            });
        });
    }

    private static void execDeleteListings(String merchantId, String accountNumber, List<AmazonProductListing> amazonProductListings,
                                           BiConsumer<AmazonProductListing, ApiResult<ListingsItemSubmissionResponse>> biConsumer,
                                           AmazonSpAccount amazonSpAccount, DeleteAmazonListingDto deleteAmazonListingDto) {
        //  加锁判定,同一套账,同一时间只允许一个账号执行下架
        String key = RedisConstant.AMAZON_ACCOUNT_DELETE_LISTING_LOCAK_PREFIX + merchantId;
        boolean lock = PublishRedissonUtils.attemptsTryLock(key, TimeUnit.SECONDS, 5, 2, 5);
        try {
            log.info("{},merchantId下架开始 链接账号：{}, lock:{}, size:{}", merchantId, accountNumber, lock, amazonProductListings.size());
            StopWatch started = StopWatch.createStarted();
            for (AmazonProductListing amazonProductListing : amazonProductListings) {
                // 下架备注
                if (deleteAmazonListingDto.getUseDefaultRemark()) {
                    amazonProductListing.setAttribute3(deleteAmazonListingDto.getDeleteRemark());
                }
                offlineTask(amazonProductListing, biConsumer, amazonSpAccount, deleteAmazonListingDto);
            }
            started.stop();
            log.info("{},merchantId下架结束 链接账号：{}, lock:{}, size:{}, 耗时：{}", merchantId, accountNumber, lock, amazonProductListings.size(), started.formatTime());
        } catch (Exception e) {
            log.error("账号：{},商家ID:{},listing size:{},按merchantId下架链接异常：{}", accountNumber, merchantId, amazonProductListings.size(), e.getMessage());
        } finally {
            if (lock) {
                PublishRedissonUtils.unlock(key);
            }
        }
    }

    /**
     * @param amazonProductListing  需要包含：账号、站点、子asin、父asin、sellerSku、itemType、货号
     * @param deleteAmazonListingDto
     * @param biConsumer
     * @param amazonSpAccount
     * @return
     */
    private static String offlineTask(AmazonProductListing amazonProductListing,
                                      BiConsumer<AmazonProductListing, ApiResult<ListingsItemSubmissionResponse>> biConsumer,
                                      AmazonSpAccount amazonSpAccount, DeleteAmazonListingDto deleteAmazonListingDto) {
        AmazonOfflineEnums.Type amazonOfflineEnumType = deleteAmazonListingDto.getAmazonOfflineEnumType();
        AmazonProcessReport report = newAmazonProcessReport(amazonProductListing, amazonOfflineEnumType.getRelationType(), deleteAmazonListingDto.getUserName());
        report.setRelationId(amazonOfflineEnumType.getCode());
        ApiResult<ListingsItemSubmissionResponse> callBackResult = new ApiResult<>();
        String msg = null;
        try {
            String accountNumber = amazonProductListing.getAccountNumber();
            String sonAsin = amazonProductListing.getSonAsin();
            String parentAsin = amazonProductListing.getParentAsin();
            String site = amazonProductListing.getSite();
            String articleNumber = amazonProductListing.getArticleNumber();
            String sellerSku = amazonProductListing.getSellerSku();
            String id = accountNumber+ "_" + sellerSku;
            Integer itemType = amazonProductListing.getItemType();

            if (null == amazonSpAccount){
                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
                amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
                // 过滤未授权到sp-api的账号
                if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
                    msg = "下架不执行，店铺未授权到sp-api,账号信息不全,请检查 marketplaceId，appName 该 accountNumber:" + accountNumber;
                    callBackResult.ofFail(1001L, msg);
                    return msg;
                }
            }

            // sellerSku解析规则为 匹配不到货号 的不进行下架
            if (StringUtils.isEmpty(articleNumber) || articleNumber.equals(NOT_ARTICLENUMER)){
                if (amazonOfflineEnumType == AmazonOfflineEnums.Type.INFRINGEMENT_WORD_DELETE) {
                    // 侵权词下架使用asin查询总销量无总销量则下架
                    Map<String, AmazonAsinSaleCountDO> asinSaleCountDOMap = esAmazonProductListingService.getSonAsinSaleCount(List.of(sonAsin));
                    AmazonAsinSaleCountDO asinSaleCountDO = asinSaleCountDOMap.get(sonAsin);
                    if (asinSaleCountDO != null && asinSaleCountDO.getSale_total_count() > 0) {
                        msg = "货号为空或者 匹配不到货号，且asin总销量不为空";
                        callBackResult.ofFail(1001L, msg);
                        return msg;
                    }
                } else {
                    msg = "货号为空或者 匹配不到货号，不下架";
                    callBackResult.ofFail(1001L, msg);
                    return msg;
                }
            }

            // 校验总销量写入时间
            String sale_id = SaleChannel.CHANNEL_AMAZON + "_" + id;
            boolean canDeleted = CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime(sale_id);
            if (BooleanUtils.isFalse(canDeleted) && AmazonOfflineEnums.Type.OFFLINE_EXCLUSIVE_NO_SALE_PRODUCTS != amazonOfflineEnumType) {
                msg = "总销量写入时间不符合，不下架";
                callBackResult.ofFail(1001L, msg);
                return msg;
            }

            // 排除FBAAsin，不区分店铺，只要是FBA的asin，则不进行下架
            EsAmazonProductListing esAmazonProductListing = new EsAmazonProductListing();
            esAmazonProductListing.setAccountNumber(accountNumber);
            esAmazonProductListing.setSonAsin(sonAsin);
            List<EsAmazonProductListing> listings = AmazonListingUtils.filterFBAExistAsin(CommonUtils.arrayAsList(esAmazonProductListing));
            if (CollectionUtils.isEmpty(listings)) {
                msg = "存在订单FBA库存管理中，不下架";
                callBackResult.ofFail(1001L, msg);
                return msg;
            }

            // 公司自注册sku，且是us站点，不下架 （5AC407513,5AC407511-B）
            boolean selfRegisteredSku = checkSelfRegisteredSku(amazonProductListing.getArticleNumber(), site);
            if (selfRegisteredSku) {
                msg = "公司自注册SKU，US站点不下架";
                callBackResult.ofFail(1001L, msg);
                return msg;
            }

            // sellerSku 包含handmade 免佣金不下架
            if (StringUtils.containsIgnoreCase(sellerSku, "handmade")) {
                msg = "sellerSku 包含handmade 免佣金不下架";
                callBackResult.ofFail(1001L, msg);
                return msg;
            }

            // 排除指定配置的sellerSku
            boolean fixedSellerSku = checkConfiguredSellerSku(amazonProductListing.getSellerSku());
            if (fixedSellerSku && AmazonOfflineEnums.Type.OFFLINE_EXCLUSIVE_NO_SALE_PRODUCTS != amazonOfflineEnumType) {
                msg = "配置指定sellerSku不下架";
                callBackResult.ofFail(1001L, msg);
                return msg;
            }

            if (null == itemType) {
                // itemType 为空不下架
                msg = String.format("Asin: %s itemType 是空无法判定是否为父体，不下架", sonAsin);
                callBackResult.ofFail(1001L, msg);
                return msg;
            }else if (BooleanUtils.isFalse(deleteAmazonListingDto.getDeleteParentAsin()) && AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode() == itemType){
                // 下架子体、单体
                msg =  String.format("Asin: %s 是父体，不下架", sonAsin);
                callBackResult.ofFail(1001L, msg);
                return msg;
            } else if (BooleanUtils.isTrue(deleteAmazonListingDto.getDeleteParentAsin())
                    && (AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode() != itemType
                        || StringUtils.isNotEmpty(parentAsin)
                    || checkAsinExistSonAsin(sonAsin))) {
                // 如果下架父体，itemType 非父体不下架
                msg =  String.format("Asin: %s 是非父体，或者该父体下存在子体，不下架", sonAsin);
                callBackResult.ofFail(1001L, msg);
                return msg;
            }


            int retryCount = deleteAmazonListingDto.getQuotaExceededRetry();
            do{
                // 请求spLocal
                callBackResult = AmazonSpLocalServiceUtils.deleteListingsItem(amazonProductListing.getSellerSku(), amazonSpAccount);
                if (callBackResult.isSuccess()) {
                    ListingsItemSubmissionResponse response = callBackResult.getResult();
                    ListingsItemSubmissionResponse.StatusEnum status = response.getStatus();
                    if (null != status && status.getValue().equalsIgnoreCase(ListingsItemSubmissionResponse.StatusEnum.ACCEPTED.getValue())) {
                        // 成功
                        String key = RedisConstant.AMAZON_LISTING_BRANDNAME_ITEMNAME_ITEMDESCRIPTION_HASH_KEY + id;
                        PublishRedisClusterUtils.del(key);
                        report.setStatus(true);
                        report.setStatusCode(ProcessingReportStatusCode.Complete.name());
                        report.setResultMsg(amazonProductListing.getAttribute3());
                        // 成功才改成下架状态
                        if (deleteAmazonListingDto.getUseDefaultRemark()) {
                            report.setResultMsg(deleteAmazonListingDto.getDeleteRemark());
                            amazonProductListing.setAttribute3(deleteAmazonListingDto.getDeleteRemark());
                        }
                        amazonProductListing.setIsOnline(false);
                        Date time = new Date();
                        amazonProductListing.setOfflineDate(time);
                        amazonProductListing.setUpdateDate(time);
                        amazonProductListingService.updateDbAndEsBySellerSkuAndAccountNumber(amazonProductListing);
                        // 变体删除绑定关系
                        if (AmazonListingitemtypeEnum.Vriant_Item.isTrue(amazonProductListing.getItemType())) {
                            amazonParentRelationshipService.deleteRelationship(amazonProductListing);
                        }
                        break;
                    } else {
                        report.setResultMsg(callBackResult.getErrorMsg());
                        report.setStatusCode(ListingsItemSubmissionResponse.StatusEnum.INVALID.getValue());
                        if (!callBackResult.getErrorMsg().contains("QuotaExceeded")) {
                            break;
                        }
                    }
                } else {
                    report.setResultMsg(callBackResult.getErrorMsg());
                }
            }while(--retryCount > 0);
            // 记录处理报告
            amazonProcessReportService.insert(report);
            callBackResult.setBusinessId(report.getId());
            if (ProcessingReportStatusCode.Invalid.name().equals(report.getStatusCode())) {
                return report.getResultMsg();
            }
        } catch (Exception e) {
            report.setResultMsg(e.getMessage());
            callBackResult.ofFail(500L, e.getMessage());
            amazonProcessReportService.insert(report);
            callBackResult.setBusinessId(report.getId());
        } finally {
            //回调
            if(biConsumer != null){
                biConsumer.accept(amazonProductListing, callBackResult);
            }
        }
        return msg;
    }

    public static boolean checkConfiguredSellerSku(String sellerSku) {
        try {
            String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "CAN_NOT_DELETE_SELLER_SKU", 5);
            if (StringUtils.isNotBlank(systemParamValue)) {
                return systemParamValue.contains(sellerSku);
            }
            return false;
        } catch (Exception e) {
            log.error("从redis获取缓存失败", e);
            return false;
        }
    }


    /**
     * 过滤出 内容不完整带下架asin
     * 1. 在线
     * 2. 非父体
     * 3. itemStatus 为内容不完整
     * @param listings 待校验Listing
     * @return 待下架Listing
     */
    public static List<AmazonProductListing> filterBadAsinListing(List<AmazonProductListing> listings) {
        if (CollectionUtils.isEmpty(listings)) {
            return listings;
        }
        return listings.stream()
                .filter(listing -> Boolean.TRUE.equals(listing.getIsOnline()))
                .filter(listing -> !AmazonListingitemtypeEnum.Maleparent_Item.isTrue(listing.getItemType()))
                .filter(listing -> BAD_ASINS.contains(listing.getItemStatus()))
                .collect(Collectors.toList());
    }

    private static boolean checkAsinExistSonAsin(String asin) {
        Boolean existSonAsin = true;
        try {
            EsAmazonProductListingRequest queryParam = new EsAmazonProductListingRequest();
            String[] fields = {"id", "parentAsin", "sonAsin"};
            queryParam.setFields(fields);
            queryParam.setParentAsin(asin);
            queryParam.setIsOnline(true);
            Page<EsAmazonProductListing> page = esAmazonProductListingService.page(queryParam, 2, 0);
            if (page != null && page.getTotalElements() <= 1) {
                existSonAsin = false;
            }
        }catch (Exception e){
            log.error("查询es出错：" +e.getMessage());
        }
        return existSonAsin;
    }

    /**
     *  检测asin是否为父asin
     * @param asin
     * @param accountNumber
     * @return
     */
    private boolean checkAsinIsParentAsin(String asin,String accountNumber,Integer itemType){
        if (null == itemType || AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode() == itemType.intValue()){
            // 查不到类型暂时当做父体处理
            return true;
        }
        Boolean parentAsinFlag = false;
        try {
            EsAmazonProductListingRequest queryParam = new EsAmazonProductListingRequest();
            String[] fields = {"id", "parentAsin", "sonAsin","itemType"};
            queryParam.setFields(fields);
            queryParam.setParentAsin(asin);
            queryParam.setAccountNumber(accountNumber);
            queryParam.setIsOnline(true);
            Page<EsAmazonProductListing> page = esAmazonProductListingService.page(queryParam, 2, 0);
            if (page != null && page.getTotalElements() == 0){
                return parentAsinFlag;
            }
            if (page != null && page.getTotalElements() > 1){
                parentAsinFlag = true;
            }else if(page != null && page.getTotalElements() == 1
                    && (page.getContent().get(0).getSonAsin() != page.getContent().get(0).getParentAsin())) {
                parentAsinFlag = true;
            }
        }catch (Exception e){
            log.error("查询es出错标记成父体：" +e.getMessage());
            parentAsinFlag = true;
        }
        return parentAsinFlag;
    }

    /**
     * 查询自注册sku
     * @return
     */
    private static List<String> getSelfRegisteredSkuList() {
        List<String> queryList = null;
        try {
            String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "SELF_REGISTERED_SKU", 5);
            List<String> selfRegisteredSkuList = CommonUtils.splitList(systemParamValue, ",");
            queryList = selfRegisteredSkuList;
        }catch (Exception e){
            log.error("从redis获取缓存失败", e);
        }
        return queryList;
    }

    private static List<String> getCatchSelfRegisteredSkuList() {
        // 当前时间
        Long currentTimeMillis = System.currentTimeMillis();
        if(CollectionUtils.isEmpty(selfRegisteredSkuList) || currentTimeMillis > staticSelfRegisteredSkuExpirationTime) {
            synchronized (staticSelfRegisteredSkuExpirationTime) {
                if(CollectionUtils.isEmpty(selfRegisteredSkuList) || currentTimeMillis > staticSelfRegisteredSkuExpirationTime) {
                    selfRegisteredSkuList = getSelfRegisteredSkuList();
                    // 30分钟后过期
                    staticSelfRegisteredSkuExpirationTime = currentTimeMillis + 30 * 60 * 1000;
                }
            }
        }
        return selfRegisteredSkuList;
    }

    /**
     * 判断是否是自注册sku，且是US站点
     * @param sku 货号
     * @param site 站点
     * @return bool
     */
    public static boolean checkSelfRegisteredSku(String sku, String site) {
        if (SELF_REGISTERD_SKU_SITE.equalsIgnoreCase(site)){
            List<String> selfRegisteredSkuList = getCatchSelfRegisteredSkuList();
            if (CollectionUtils.isEmpty(selfRegisteredSkuList)){
                // 如果是异常暂时跳过
                return true;
            }
             return selfRegisteredSkuList.contains(sku);
        }
        return false;
    }

    /**
     * 下架处理报告
     * @param amazonProductListing
     * @param relationType
     * @param createBy
     * @return
     */
    public static AmazonProcessReport newAmazonProcessReport(AmazonProductListing amazonProductListing, String relationType, String createBy) {
        AmazonProcessReport report = new AmazonProcessReport();
        report.setFeedType( FeedType.DELETET_LISTINGS_DATA);
        report.setCreationDate(new Date());
        report.setAccountNumber(amazonProductListing.getAccountNumber());
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
        report.setDataValue(amazonProductListing.getSellerSku());
        // 默认失败
        report.setStatus(false);
        report.setRelationType(relationType);
        report.setCreatedBy(createBy);
        return report;
    }

}