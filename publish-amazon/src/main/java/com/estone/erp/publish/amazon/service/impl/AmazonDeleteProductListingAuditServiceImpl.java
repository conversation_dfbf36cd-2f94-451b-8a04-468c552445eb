package com.estone.erp.publish.amazon.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonDeleteAuditVo;
import com.estone.erp.publish.amazon.enums.AmazonDeleteAuditEnums;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.mapper.AmazonDeleteProductListingAuditMapper;
import com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAudit;
import com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAuditCriteria;
import com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAuditExample;
import com.estone.erp.publish.amazon.model.dto.AmazonDeleteProductListingAuditVo;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.service.AmazonDeleteProductListingAuditService;
import com.estone.erp.publish.amazon.util.DeleteAmazonListingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR> amazon_delete_product_listing_audit
 * 2024-01-19 16:54:16
 */
@Service("amazonDeleteProductListingAuditService")
@Slf4j
public class AmazonDeleteProductListingAuditServiceImpl implements AmazonDeleteProductListingAuditService {
    @Resource
    private AmazonDeleteProductListingAuditMapper amazonDeleteProductListingAuditMapper;

    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;

    @Override
    public int countByExample(AmazonDeleteProductListingAuditExample example) {
        Assert.notNull(example, "example is null!");
        return amazonDeleteProductListingAuditMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonDeleteProductListingAuditVo> search(CQuery<AmazonDeleteProductListingAuditCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonDeleteProductListingAuditCriteria query = cquery.getSearch();
        AmazonDeleteProductListingAuditExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonDeleteProductListingAuditMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        if (StringUtils.isNotBlank(cquery.getSort())) {
            example.setOrderByClause(cquery.getSort() + " " + cquery.getOrder());
        }else {
            example.setOrderByClause(" update_date desc, create_date desc ");
        }

        List<AmazonDeleteProductListingAudit> amazonDeleteProductListingAudits = amazonDeleteProductListingAuditMapper.selectByExample(example);

        List<String> accountNumbers = amazonDeleteProductListingAudits.stream().map(AmazonDeleteProductListingAudit::getAccountNumber).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumbers, SaleChannel.CHANNEL_AMAZON);

        List<String> esIds = amazonDeleteProductListingAudits.stream().map(audit -> audit.getAccountNumber() + "_" + audit.getSellerSku()).collect(Collectors.toList());
        Map<String, EsAmazonProductListing> esProductListings = getEsProductListings(esIds);


        List<AmazonDeleteProductListingAuditVo> collect = amazonDeleteProductListingAudits.stream().map(a -> {
            AmazonDeleteProductListingAuditVo amazonDeleteProductListingAuditVo = new AmazonDeleteProductListingAuditVo();
            BeanUtils.copyProperties(a, amazonDeleteProductListingAuditVo);

            EsAmazonProductListing esAmazonProductListing = esProductListings.get(a.getAccountNumber() + "_" + a.getSellerSku());
            if (esAmazonProductListing != null) {
                amazonDeleteProductListingAuditVo.setIsOnline(esAmazonProductListing.getIsOnline());
            }

            if (saleSuperiorMap.get(a.getAccountNumber()) != null) {
                Triple<String, String, String> superior = saleSuperiorMap.get(a.getAccountNumber());
                amazonDeleteProductListingAuditVo.setSalesman(superior.getLeft());
                amazonDeleteProductListingAuditVo.setSalesTeamLeader(superior.getMiddle());
                amazonDeleteProductListingAuditVo.setSalesSupervisorName(superior.getRight());
            }

            return amazonDeleteProductListingAuditVo;
        }).collect(Collectors.toList());

        // 组装结果
        CQueryResult<AmazonDeleteProductListingAuditVo> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(collect);
        return result;
    }

    private Map<String, EsAmazonProductListing> getEsProductListings(List<String> esIds) {
        if (CollectionUtils.isEmpty(esIds)) {
            return Collections.emptyMap();
        }
        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest.setIdList(esIds);
        esAmazonProductListingRequest.setFields(new String[]{"id","accountNumber", "isOnline", "attribute3"});
        List<EsAmazonProductListing> esAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
        Map<String, EsAmazonProductListing> esProductListings = new HashMap<>();
        for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListings) {
            esProductListings.put(esAmazonProductListing.getId(), esAmazonProductListing);
        }
        return esProductListings;
    }

    @Override
    public AmazonDeleteProductListingAudit selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return amazonDeleteProductListingAuditMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonDeleteProductListingAudit> selectByExample(AmazonDeleteProductListingAuditExample example) {
        Assert.notNull(example, "example is null!");
        return amazonDeleteProductListingAuditMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonDeleteProductListingAudit record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        return amazonDeleteProductListingAuditMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonDeleteProductListingAudit record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonDeleteProductListingAuditMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void batchUpdateByPrimaryKeySelective(List<AmazonDeleteProductListingAudit> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        amazonDeleteProductListingAuditMapper.batchUpdateByPrimaryKeySelective(updateList);
    }

    @Override
    public int updateByExampleSelective(AmazonDeleteProductListingAudit record, AmazonDeleteProductListingAuditExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonDeleteProductListingAuditMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonDeleteProductListingAuditMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public int deleteByExample(AmazonDeleteProductListingAuditExample example) {
        Assert.notNull(example, "example is null!");
        return amazonDeleteProductListingAuditMapper.deleteByExample(example);
    }

    @Override
    public List<AmazonDeleteProductListingAuditVo> downloadPage(AmazonDeleteProductListingAuditCriteria search, int pageIndex, int pageSize) {
        AmazonDeleteProductListingAuditExample example = search.getExample();
        example.setOffset(pageIndex * pageSize);
        example.setLimit(pageSize);
        List<AmazonDeleteProductListingAudit> amazonDeleteProductListingAudits = amazonDeleteProductListingAuditMapper.selectByExample(example);

        List<String> accountNumbers = amazonDeleteProductListingAudits.stream().map(AmazonDeleteProductListingAudit::getAccountNumber).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumbers, SaleChannel.CHANNEL_AMAZON);

        List<String> esIds = amazonDeleteProductListingAudits.stream().map(audit -> audit.getAccountNumber() + "_" + audit.getSellerSku()).collect(Collectors.toList());
        Map<String, EsAmazonProductListing> esProductListings = getEsProductListings(esIds);


        return amazonDeleteProductListingAudits.stream().map(a -> {
            AmazonDeleteProductListingAuditVo amazonDeleteProductListingAuditVo = new AmazonDeleteProductListingAuditVo();
            BeanUtils.copyProperties(a, amazonDeleteProductListingAuditVo);

            EsAmazonProductListing esAmazonProductListing = esProductListings.get(a.getAccountNumber() + "_" + a.getSellerSku());
            if (esAmazonProductListing != null) {
                amazonDeleteProductListingAuditVo.setOfflineReason(esAmazonProductListing.getAttribute3());
                amazonDeleteProductListingAuditVo.setIsOnline(esAmazonProductListing.getIsOnline());
            }
            if (saleSuperiorMap.get(a.getAccountNumber()) != null) {
                Triple<String, String, String> superior = saleSuperiorMap.get(a.getAccountNumber());
                amazonDeleteProductListingAuditVo.setSalesman(superior.getLeft());
                amazonDeleteProductListingAuditVo.setSalesTeamLeader(superior.getMiddle());
                amazonDeleteProductListingAuditVo.setSalesSupervisorName(superior.getRight());
            }
            return amazonDeleteProductListingAuditVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<String> getUnAuditedSubmitBy(int status) {
        return amazonDeleteProductListingAuditMapper.getUnAuditedSubmitBy(status);
    }

    @Override
    public void defaultPass(List<Integer> ids) {
        amazonDeleteProductListingAuditMapper.defaultPass(ids);
    }

    @Override
    public void batchGenerationReview(List<AmazonProductListing> batchRetireProductListing, String remark) {
        boolean isLeader = false;
        ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.tokenUser();
        if (newUserApiResult.isSuccess()) {
            NewUser result = newUserApiResult.getResult();
            if (StringUtils.containsIgnoreCase(result.getPositionName(), RoleConstant.GROUP_LEADER)) {
                isLeader = true;
            }
        }

        Set<String> collect = batchRetireProductListing.stream().map(AmazonProductListing::getAccountNumber).collect(Collectors.toSet());
        Map<String, SalesmanAccountDetail> salesmanAccountDetailMapByEs = EsAccountUtils.getSalesmanAccountDetailMapByEs(new ArrayList<>(collect), SaleChannel.CHANNEL_AMAZON);
        for (AmazonProductListing amazonProductListing : batchRetireProductListing) {
            String accountNumber = amazonProductListing.getAccountNumber();
            String sellerSku = amazonProductListing.getSellerSku();
            EsAmazonProductListing esAmazonProductListing = esAmazonProductListingService.findAllById(accountNumber + "_" + sellerSku);
            if (esAmazonProductListing == null) {
                continue;
            }

            AmazonDeleteProductListingAudit amazonDeleteProductListingAudit = init(esAmazonProductListing, remark, salesmanAccountDetailMapByEs);
            if (isLeader) {
                amazonDeleteProductListingAudit.setStatus(2);
                amazonDeleteProductListingAudit.setSalesTeamLeaderAuditTime(amazonDeleteProductListingAudit.getSubmitTime());
                amazonDeleteProductListingAudit.setSalesTeamLeaderAuditBy(amazonDeleteProductListingAudit.getSubmitBy());
            }
            amazonDeleteProductListingAuditMapper.insert(amazonDeleteProductListingAudit);
        }
    }

    @Override
    public ApiResult<?> leaderAudit(AmazonDeleteAuditVo amazonDeleteAuditVo) {
        List<Integer> ids = amazonDeleteAuditVo.getIds();
        AmazonDeleteProductListingAuditExample example = new AmazonDeleteProductListingAuditExample();
        example.createCriteria().andIdIn(ids);
        List<AmazonDeleteProductListingAudit> amazonDeleteProductListingAudits = amazonDeleteProductListingAuditMapper.selectByExample(example);
        for (AmazonDeleteProductListingAudit amazonDeleteProductListingAudit : amazonDeleteProductListingAudits) {
            Integer status = amazonDeleteProductListingAudit.getStatus();
            if (!AmazonDeleteAuditEnums.AuditStatus.WAIT_LEADER.isTrue(status)) {
                return ApiResult.newError("审核状态必须要待组长审核才可以进行组长审核");
            }
        }

        Date date = new Date();
        String userName = WebUtils.getUserName();
        String remark = amazonDeleteAuditVo.getRemark();
        Integer confirmStatus = amazonDeleteAuditVo.getStatus();
        // 1 待组长审核 2 代主管审核 3 审核通过已下架 4 审核不通过
        AmazonDeleteAuditEnums.AuditStatus auditStatus = AmazonDeleteAuditEnums.Confirm.YES.isTrue(confirmStatus)
                ? AmazonDeleteAuditEnums.AuditStatus.WAIT_SUPERVISOR : AmazonDeleteAuditEnums.AuditStatus.REJECT;
        List<Integer> collect = amazonDeleteProductListingAudits.stream().map(AmazonDeleteProductListingAudit::getId).collect(Collectors.toList());
        example = new AmazonDeleteProductListingAuditExample();
        example.createCriteria().andIdIn(collect);

        AmazonDeleteProductListingAudit updateAuditRecord = new AmazonDeleteProductListingAudit();
        updateAuditRecord.setSalesTeamLeaderAuditBy(userName);
        updateAuditRecord.setSalesTeamLeaderAuditTime(new Timestamp(date.getTime()));
        updateAuditRecord.setUpdatedBy(userName);
        updateAuditRecord.setUpdateDate(new Timestamp(date.getTime()));
        updateAuditRecord.setAuditReason(remark);
        updateAuditRecord.setStatus(auditStatus.getCode());
        updateByExampleSelective(updateAuditRecord, example);

        return ApiResult.newSuccess();
    }

    @Override
    public ApiResult<?> supervisorAudit(AmazonDeleteAuditVo amazonDeleteAuditVo) {
        List<Integer> ids = amazonDeleteAuditVo.getIds();
        AmazonDeleteProductListingAuditExample example = new AmazonDeleteProductListingAuditExample();
        example.createCriteria().andIdIn(ids);
        List<AmazonDeleteProductListingAudit> amazonDeleteProductListingAudits = amazonDeleteProductListingAuditMapper.selectByExample(example);
        for (AmazonDeleteProductListingAudit amazonDeleteProductListingAudit : amazonDeleteProductListingAudits) {
            Integer status = amazonDeleteProductListingAudit.getStatus();
            if (!AmazonDeleteAuditEnums.AuditStatus.WAIT_SUPERVISOR.isTrue(status)
                    && !AmazonDeleteAuditEnums.AuditStatus.WAIT_LEADER.isTrue(status) ) {
                return ApiResult.newError("审核状态必须要待组长审核、待主管审核才可以进行主管审核");
            }
        }
        Date date = new Date();
        String userName = WebUtils.getUserName();
        String remark = amazonDeleteAuditVo.getRemark();
        Integer confirmStatus = amazonDeleteAuditVo.getStatus();
        // 1 待组长审核 2 待主管审核 3 审核通过已下架 4 审核不通过 5 审核中 6 默认通过
        AmazonDeleteAuditEnums.AuditStatus auditStatus = AmazonDeleteAuditEnums.Confirm.YES.isTrue(confirmStatus)
                ? AmazonDeleteAuditEnums.AuditStatus.AUDITING : AmazonDeleteAuditEnums.AuditStatus.REJECT;

        List<Integer> collect = amazonDeleteProductListingAudits.stream().map(AmazonDeleteProductListingAudit::getId).collect(Collectors.toList());
        example = new AmazonDeleteProductListingAuditExample();
        example.createCriteria().andIdIn(collect);

        AmazonDeleteProductListingAudit updateAuditRecord = new AmazonDeleteProductListingAudit();
        updateAuditRecord.setSalesSupervisorAuditBy(userName);
        updateAuditRecord.setSalesSupervisorAuditTime(new Timestamp(date.getTime()));
        updateAuditRecord.setUpdatedBy(userName);
        updateAuditRecord.setUpdateDate(new Timestamp(date.getTime()));
        updateAuditRecord.setAuditReason(remark);
        updateAuditRecord.setStatus(auditStatus.getCode());
        updateByExampleSelective(updateAuditRecord, example);

        // 审核中状态
        if (auditStatus == AmazonDeleteAuditEnums.AuditStatus.AUDITING) {
            List<String> listingIds = amazonDeleteProductListingAudits.stream().map(a -> a.getAccountNumber() + "_" + a.getSellerSku()).collect(Collectors.toList());
            EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
            request.setIdList(listingIds);
            request.setFields(EsAmazonProductListingRequest.allFields);
            List<EsAmazonProductListing> esAmazonProductListing = esAmazonProductListingService.getEsAmazonProductListing(request);

            DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
            deleteAmazonListingDto.setUseDefaultRemark(false);
            deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.Sale_Operate_Listing_Delete);


            List<AmazonProductListing>  deleteList = new ArrayList<>();
            Map<String, Integer> auditMap = new HashMap<>();

            Map<String, EsAmazonProductListing> idListingMap = esAmazonProductListing.stream()
                    .collect(Collectors.toMap(EsAmazonProductListing::getId, a -> a, (oldv, newV) -> newV));
            for (AmazonDeleteProductListingAudit amazonDeleteProductListingAudit : amazonDeleteProductListingAudits) {
                EsAmazonProductListing esListing = idListingMap.get(amazonDeleteProductListingAudit.getAccountNumber() + "_" + amazonDeleteProductListingAudit.getSellerSku());
                if (esListing == null) {
                    continue;
                }
                AmazonProductListing amazonProductListing = new AmazonProductListing();
                BeanUtils.copyProperties(esListing, amazonProductListing);
                amazonProductListing.setAttribute3(amazonDeleteProductListingAudit.getOfflineReason());
                deleteAmazonListingDto.setRemarkParam(amazonDeleteProductListingAudit.getOfflineReason());
                deleteList.add(amazonProductListing);
                auditMap.put(esListing.getId(), amazonDeleteProductListingAudit.getId());
            }

            DeleteAmazonListingUtils.batchRetireProductSingle(deleteList, (a, apiResult) -> {
                boolean success = apiResult.isSuccess();
                Integer auditId = auditMap.get(a.getAccountNumber() + "_" + a.getSellerSku());
                if (success) {
                    AmazonDeleteProductListingAudit deleteAuditRecord = new AmazonDeleteProductListingAudit();
                    deleteAuditRecord.setOfflineDate(new Timestamp(a.getOfflineDate().getTime()));
                    deleteAuditRecord.setIsOnline(false);
                    deleteAuditRecord.setId(auditId);
                    deleteAuditRecord.setStatus(AmazonDeleteAuditEnums.AuditStatus.PASS_OFF.getCode());
                    updateByPrimaryKeySelective(deleteAuditRecord);
                } else {
                    AmazonDeleteProductListingAudit deleteAuditRecord = new AmazonDeleteProductListingAudit();
                    deleteAuditRecord.setAuditReason(apiResult.getErrorMsg());
                    deleteAuditRecord.setStatus(AmazonDeleteAuditEnums.AuditStatus.REJECT.getCode());
                    deleteAuditRecord.setId(auditId);
                    updateByPrimaryKeySelective(deleteAuditRecord);
                }
            },deleteAmazonListingDto);
        }
        return ApiResult.newSuccess();
    }

    private AmazonDeleteProductListingAudit init(EsAmazonProductListing esAmazonProductListing, String remark, Map<String, SalesmanAccountDetail> salesmanAccountDetailMap) {
        Date date = new Date();
        String userName = WebUtils.getUserName();
        AmazonDeleteProductListingAudit amazonDeleteProductListingAudit = new AmazonDeleteProductListingAudit();
        SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(esAmazonProductListing.getAccountNumber());
        amazonDeleteProductListingAudit.setSalesId(EsAccountUtils.getSaleId(salesmanAccountDetail));

        // 初始化信息
        amazonDeleteProductListingAudit.setSyncDate(new Timestamp(date.getTime()));
        amazonDeleteProductListingAudit.setSubmitBy(userName);
        amazonDeleteProductListingAudit.setSubmitTime(new Timestamp(date.getTime()));
        amazonDeleteProductListingAudit.setCreateDate(new Timestamp(date.getTime()));
        amazonDeleteProductListingAudit.setCreatedBy(userName);
        amazonDeleteProductListingAudit.setOfflineReason(remark);
        amazonDeleteProductListingAudit.setStatus(1);

        // 基础信息
        amazonDeleteProductListingAudit.setAccountNumber(esAmazonProductListing.getAccountNumber());
        amazonDeleteProductListingAudit.setMainSku(esAmazonProductListing.getMainSku());
        amazonDeleteProductListingAudit.setArticleNumber(esAmazonProductListing.getArticleNumber());
        amazonDeleteProductListingAudit.setParentAsin(esAmazonProductListing.getParentAsin());
        amazonDeleteProductListingAudit.setPrice(esAmazonProductListing.getPrice());
        amazonDeleteProductListingAudit.setSonAsin(esAmazonProductListing.getSonAsin());
        amazonDeleteProductListingAudit.setSellerSku(esAmazonProductListing.getSellerSku());
        amazonDeleteProductListingAudit.setSkuStatus(esAmazonProductListing.getSkuStatus());
        amazonDeleteProductListingAudit.setIsOnline(esAmazonProductListing.getIsOnline());
        amazonDeleteProductListingAudit.setMainImage(esAmazonProductListing.getMainImage());

        // 销量
        amazonDeleteProductListingAudit.setOrder24hCount(esAmazonProductListing.getOrder_24H_count());
        amazonDeleteProductListingAudit.setOrderLast7dCount(esAmazonProductListing.getOrder_last_7d_count());
        amazonDeleteProductListingAudit.setOrderLast14dCount(esAmazonProductListing.getOrder_last_14d_count());
        amazonDeleteProductListingAudit.setOrderLast30dCount(esAmazonProductListing.getOrder_last_30d_count());
        amazonDeleteProductListingAudit.setOrderNumTotal(esAmazonProductListing.getOrder_num_total());

        // 禁售
        amazonDeleteProductListingAudit.setForbidChannel(esAmazonProductListing.getForbidChannel());
        amazonDeleteProductListingAudit.setInfringementObj(esAmazonProductListing.getInfringementObj());
        amazonDeleteProductListingAudit.setProhibitionSite(esAmazonProductListing.getNormalSale());
        amazonDeleteProductListingAudit.setInfringementTypename(esAmazonProductListing.getInfringementTypename());
        return amazonDeleteProductListingAudit;
    }
}