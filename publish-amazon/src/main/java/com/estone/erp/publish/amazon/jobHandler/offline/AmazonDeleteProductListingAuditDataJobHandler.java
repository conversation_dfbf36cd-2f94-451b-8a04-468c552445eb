package com.estone.erp.publish.amazon.jobHandler.offline;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.enums.AmazonDeleteAuditEnums;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAudit;
import com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAuditExample;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.service.AmazonDeleteProductListingAuditService;
import com.estone.erp.publish.amazon.util.DeleteAmazonListingUtils;
import com.estone.erp.publish.common.WebSocketRequestDTO;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.system.erpCommon.ErpCommonWebSocketApiClient;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.SuperEmployeeInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 删除超一个月已下架数据定时任务
 */
@Component
public class AmazonDeleteProductListingAuditDataJobHandler extends AbstractJobHandler {

    @Resource
    private AmazonDeleteProductListingAuditService amazonDeleteProductListingAuditService;
    @Resource
    private ErpCommonWebSocketApiClient webSocketClient;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;

    public AmazonDeleteProductListingAuditDataJobHandler() {
        super("amazonDeleteProductListingAuditDataJobHandler");
    }

    @Data
    public static class InnerParam {
        /**
         * 审核时间
         */
        private Integer auditHour;

        /**
         * 超时时间
         */
        private Integer auditTimeOut;

        /**
         * 删除指定日期一个月后审核了的数据
         */
        private String day;

        /**
         * key 为推送时间（24小时），
         * value 过期时间（单位小时）
         *
         */
        private Map<Integer, Integer> pushTimeMap;
    }

    @XxlJob("amazonDeleteProductListingAuditDataJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("amazonDeleteProductListingAuditDataJobHandler 定时统计每天生成的刊登队列");
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            innerParam = new InnerParam();
        }

        // 匹配推送时间
        pushUnAuditedData(innerParam);

        // 24小时默认审核通过
        defaultPassUnAuditedData(innerParam);

        // 删除过期数据
        deleteExpiredData(innerParam);

        return ReturnT.SUCCESS;
    }

    private void defaultPassUnAuditedData(InnerParam innerParam) {
        int auditHour = innerParam.getAuditHour() == null ? 20 : innerParam.getAuditHour();
        LocalDateTime now = LocalDateTime.now();
        int currentHour = now.getHour();
        if (currentHour != auditHour) {
            XxlJobLogger.log("当前时间 {} 不是审核时间，不执行默认审核操作", currentHour);
            return;
        }
        LocalDateTime currentTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(auditHour, 0, 0));
        LocalDateTime timeOutTime = currentTime.minusHours(innerParam.getAuditTimeOut() == null ? 24 : innerParam.getAuditTimeOut());
        XxlJobLogger.log("当前时间 {} 符合审核时间，默认审核未审核通过且是否在线为否的数据，超时时间：{}", currentHour, timeOutTime);
        while (true) {
            AmazonDeleteProductListingAuditExample example = new AmazonDeleteProductListingAuditExample();
            example.setLimit(500);
            AmazonDeleteProductListingAuditExample.Criteria criteria = example.createCriteria();
            criteria.andSubmitTimeLessThan(Timestamp.valueOf(timeOutTime));
            criteria.andStatusLessThan(AmazonDeleteAuditEnums.AuditStatus.PASS_OFF.getCode());
            criteria.andIsOnlineEqualTo(true);

            List<AmazonDeleteProductListingAudit> amazonDeleteProductListingAudits = amazonDeleteProductListingAuditService.selectByExample(example);
            if (CollectionUtils.isEmpty(amazonDeleteProductListingAudits)) {
                break;
            }

            List<Integer> ids = amazonDeleteProductListingAudits.stream().map(AmazonDeleteProductListingAudit::getId).collect(Collectors.toList());
            amazonDeleteProductListingAuditService.defaultPass(ids);

            List<String> listingIds = amazonDeleteProductListingAudits.stream().map(a -> a.getAccountNumber() + "_" + a.getSellerSku()).collect(Collectors.toList());
            EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
            request.setIdList(listingIds);
            request.setFields(EsAmazonProductListingRequest.allFields);
            List<EsAmazonProductListing> esAmazonProductListing = esAmazonProductListingService.getEsAmazonProductListing(request);

            DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
            deleteAmazonListingDto.setUseDefaultRemark(false);
            deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.Sale_Operate_Listing_Delete);


            List<AmazonProductListing>  deleteList = new ArrayList<>();
            Map<String, Integer> auditMap = new HashMap<>();

            Map<String, EsAmazonProductListing> idListingMap = esAmazonProductListing.stream()
                    .collect(Collectors.toMap(EsAmazonProductListing::getId, a -> a, (oldv, newV) -> newV));
            for (AmazonDeleteProductListingAudit amazonDeleteProductListingAudit : amazonDeleteProductListingAudits) {
                EsAmazonProductListing esListing = idListingMap.get(amazonDeleteProductListingAudit.getAccountNumber() + "_" + amazonDeleteProductListingAudit.getSellerSku());
                if (esListing == null) {
                    continue;
                }
                AmazonProductListing amazonProductListing = new AmazonProductListing();
                BeanUtils.copyProperties(esListing, amazonProductListing);
                amazonProductListing.setAttribute3(amazonDeleteProductListingAudit.getOfflineReason());
                deleteAmazonListingDto.setRemarkParam(amazonDeleteProductListingAudit.getOfflineReason());
                deleteList.add(amazonProductListing);
                auditMap.put(esListing.getId(), amazonDeleteProductListingAudit.getId());
            }
            DataContextHolder.setUsername("admin");
            DeleteAmazonListingUtils.batchRetireProductSingle(deleteList, (a, apiResult) -> {
                boolean success = apiResult.isSuccess();
                Integer auditId = auditMap.get(a.getAccountNumber() + "_" + a.getSellerSku());
                if (success) {
                    AmazonDeleteProductListingAudit deleteAuditRecord = new AmazonDeleteProductListingAudit();
                    deleteAuditRecord.setOfflineDate(new Timestamp(a.getOfflineDate().getTime()));
                    deleteAuditRecord.setIsOnline(false);
                    deleteAuditRecord.setId(auditId);
                    deleteAuditRecord.setStatus(AmazonDeleteAuditEnums.AuditStatus.DEFAULT_PASS.getCode());
                    deleteAuditRecord.setAuditReason("24小时未审核默认通过下架");
                    amazonDeleteProductListingAuditService.updateByPrimaryKeySelective(deleteAuditRecord);
                } else {
                    AmazonDeleteProductListingAudit deleteAuditRecord = new AmazonDeleteProductListingAudit();
                    deleteAuditRecord.setAuditReason(apiResult.getErrorMsg());
                    deleteAuditRecord.setStatus(AmazonDeleteAuditEnums.AuditStatus.REJECT.getCode());
                    deleteAuditRecord.setId(auditId);
                    amazonDeleteProductListingAuditService.updateByPrimaryKeySelective(deleteAuditRecord);
                }
            },deleteAmazonListingDto);

        }
    }

    private void pushUnAuditedData(InnerParam innerParam) {
        Map<Integer, Integer> pushTimeMap = innerParam.getPushTimeMap();
        if (MapUtils.isEmpty(pushTimeMap)) {
            XxlJobLogger.log("推送配置为空，不执行推送操作");
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        int hour = now.getHour();

        // 过期时间
        Integer expiredTimeHour = pushTimeMap.get(hour);
        if (expiredTimeHour == null) {
            XxlJobLogger.log("当前时间 {} 不在推送时间范围内，不执行推送操作", hour);
            return;
        }
        // 过期时间
        LocalDateTime expiredDateTime = now.plusHours(expiredTimeHour);
        // 查找所有未审核通过且是否在线是的数据

        // 组长
        List<Integer> pushEmployeeIdList = new ArrayList<>();
        addUnAuditedLeader(pushEmployeeIdList);
        // 主管
        addUnAuditedSupervisorLeader(pushEmployeeIdList);

        // 推送消息
        if (CollectionUtils.isNotEmpty(pushEmployeeIdList)) {
            pushEmployeeIdList.stream().distinct().forEach(employeeId -> pushWebMessage(employeeId, expiredDateTime));
        }
    }

    private void addUnAuditedSupervisorLeader(List<Integer> pushEmployeeIdList) {
        List<String> unAuditedSubmitBySupervisorList = amazonDeleteProductListingAuditService.getUnAuditedSubmitBy(AmazonDeleteAuditEnums.AuditStatus.WAIT_SUPERVISOR.getCode());
        if (CollectionUtils.isEmpty(unAuditedSubmitBySupervisorList)) {
            return;
        }
        // 根据工号获取员工信息
        unAuditedSubmitBySupervisorList.forEach(submitBy -> {
            ApiResult<SuperEmployeeInfo> apiResult = NewUsermgtUtils.getAllSuperByEmpNo(submitBy);
            if (!apiResult.isSuccess()) {
                XxlJobLogger.log("获取员工信息失败，员工号：{}", submitBy);
                return;
            }

            SuperEmployeeInfo superEmployeeInfo = apiResult.getResult();
            if (superEmployeeInfo == null) {
                XxlJobLogger.log("获取员工信息失败，员工号：{}", submitBy);
                return;
            }

            String positionName = superEmployeeInfo.getPositionName();
            if (positionName.contains("主管")) {
                pushEmployeeIdList.add(Integer.valueOf(superEmployeeInfo.getSuperEmployeeId()));
                return;
            }

            List<SuperEmployeeInfo> leaderList = superEmployeeInfo.getLeaderList();
            if (CollectionUtils.isEmpty(leaderList)) {
                return;
            }
            SuperEmployeeInfo leader = leaderList.get(0);
            if (leader.getPositionName().contains("主管")) {
                pushEmployeeIdList.add(Integer.valueOf(leader.getSuperEmployeeId()));
                return;
            }
            List<SuperEmployeeInfo> superEmployeeInfoList = leader.getLeaderList();
            if (CollectionUtils.isEmpty(superEmployeeInfoList)) {
                return;
            }
            SuperEmployeeInfo superLeader = superEmployeeInfoList.get(0);
            pushEmployeeIdList.add(Integer.valueOf(superLeader.getSuperEmployeeId()));
        });
    }

    private void addUnAuditedLeader(List<Integer> pushEmployeeIdList) {
        List<String> unAuditedSubmitByList = amazonDeleteProductListingAuditService.getUnAuditedSubmitBy(AmazonDeleteAuditEnums.AuditStatus.WAIT_LEADER.getCode());
        if (CollectionUtils.isEmpty(unAuditedSubmitByList)) {
            return;
        }

        // 根据工号获取员工信息
        unAuditedSubmitByList.forEach(submitBy -> {
            ApiResult<SuperEmployeeInfo> apiResult = NewUsermgtUtils.getAllSuperByEmpNo(submitBy);
            if (!apiResult.isSuccess()) {
                XxlJobLogger.log("获取员工信息失败，员工号：{}", submitBy);
                return;
            }

            SuperEmployeeInfo superEmployeeInfo = apiResult.getResult();
            if (superEmployeeInfo == null) {
                XxlJobLogger.log("获取员工信息失败，员工号：{}", submitBy);
                return;
            }

            String positionName1 = superEmployeeInfo.getPositionName();
            if (positionName1.contains("组长")) {
                pushEmployeeIdList.add(Integer.valueOf(superEmployeeInfo.getSuperEmployeeId()));
                return;
            }

            String positionName = superEmployeeInfo.getPositionName();
            if (positionName.contains("主管")) {
                pushEmployeeIdList.add(Integer.valueOf(superEmployeeInfo.getSuperEmployeeId()));
                return;
            }

            List<SuperEmployeeInfo> leaderList = superEmployeeInfo.getLeaderList();
            if (CollectionUtils.isEmpty(leaderList)) {
                return;
            }
            SuperEmployeeInfo leader = leaderList.get(0);
            pushEmployeeIdList.add(Integer.valueOf(leader.getSuperEmployeeId()));
        });

    }

    private void pushWebMessage(Integer employeeId, LocalDateTime expiredDate) {
        try {
            WebSocketRequestDTO webMessage = new WebSocketRequestDTO();
            webMessage.setType("read_待下架数据审核");
            webMessage.setUserId(Math.toIntExact(employeeId));
            webMessage.setContent("存在删除未审核的数据，请及时审核");
            webMessage.setExpiredDate(LocalDateTimeUtil.passLocalDateTimeToDate(expiredDate));
            webSocketClient.sendWebSocketMessage(webMessage);
            XxlJobLogger.log("推送消息成功，员工ID：{}, 过期时间：{}", employeeId, expiredDate);
        } catch (Exception e) {
            XxlJobLogger.log("推送消息失败，员工ID：{}, 过期时间：{}, 原因：{}", employeeId, expiredDate, e.getMessage());
        }
    }


    private void deleteExpiredData(InnerParam innerParam) {
        if (innerParam.getDay() == null) {
            innerParam.setDay(DateUtils.format(new Date(), "yyyy-MM-dd"));
        }
        LocalDate date = LocalDate.parse(innerParam.getDay());
        // 上一个月
        LocalDateTime lastMonthDateTime = LocalDateTime.of(date, LocalTime.MIN).minusMonths(1);
        XxlJobLogger.log("amazonDeleteProductListingAuditDataJobHandler： 将删除小于 {} 日期的提交审核时间且审核通过已下架且是否在线为否的数据", lastMonthDateTime);
        AmazonDeleteProductListingAuditExample example = new AmazonDeleteProductListingAuditExample();
        AmazonDeleteProductListingAuditExample.Criteria criteria = example.createCriteria();
        criteria.andSubmitTimeLessThan(Timestamp.valueOf(lastMonthDateTime));
        criteria.andStatusIn(List.of(AmazonDeleteAuditEnums.AuditStatus.PASS_OFF.getCode(),AmazonDeleteAuditEnums.AuditStatus.DEFAULT_PASS.getCode()));
        criteria.andIsOnlineEqualTo(false);
        int i = amazonDeleteProductListingAuditService.deleteByExample(example);
        XxlJobLogger.log("amazonDeleteProductListingAuditDataJobHandler： 删除完毕，删除个数：{}", i);
    }
}
