package com.estone.erp.publish.amazon.jobHandler.offline;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.service.AmazonAccountPublishConfigService;
import com.estone.erp.publish.amazon.util.AmazonSpecialTagUtils;
import com.estone.erp.publish.amazon.util.DeleteAmazonListingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.system.order.OrderUtils;
import com.estone.erp.publish.system.order.modle.AsinSalesVolume;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SpecialGoods;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * 特殊标签下架
 * - 特供产品限时下架
 */
@Slf4j
@Component
public class AmazonSpecialTypeCodeNoSaleOfflineJobHandler extends AbstractJobHandler {

    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private SingleItemEsService singleItemEsService;
    @Resource
    private AmazonAccountPublishConfigService amazonAccountPublishConfigService;

    public AmazonSpecialTypeCodeNoSaleOfflineJobHandler() {
        super(AmazonSpecialTypeCodeNoSaleOfflineJobHandler.class.getName());
    }


    @Data
    static class InnerParam {
        private Integer dayRange;
        private String saleNumberField;
        private String platformSupervisor;
        private List<String> accountNumbers;
        private List<String> sellerSkuList;
    }

    @Override
    @XxlJob("amazonSpecialTypeCodeNoSaleOfflineJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            return ReturnT.SUCCESS;
        }

        // 获取特供店铺账号列表
        SystemParam systemParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_AMAZON, "amazon_param", "AMZ_Special_Goods_Account");
        final List<String> specialAccounts = new ArrayList<>();
        if (systemParam != null && StringUtils.isNotBlank(systemParam.getParamValue())) {
            specialAccounts.addAll(Arrays.asList(systemParam.getParamValue().split(",")));
        }
        // 过滤出吴蓓蕾下的账号
        List<String> userManagedAccountNumbersCache = amazonAccountPublishConfigService.getEmployeeManagedAccountNumbers(List.of(innerParam.getPlatformSupervisor()));
        if (CollectionUtils.isEmpty(userManagedAccountNumbersCache)) {
            XxlJobLogger.log("用户管理的账号列表为空: {}", innerParam.getPlatformSupervisor());
            return ReturnT.SUCCESS;
        }

        List<String> accountNumbers = specialAccounts.stream()
                .filter(userManagedAccountNumbersCache::contains)
                .filter(accountNumber -> {
                    if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers())) {
                        return innerParam.getAccountNumbers().contains(accountNumber);
                    }
                    return true;
                })
                .collect(Collectors.toList());


        XxlJobLogger.log("可执行特供账号列表: {}", accountNumbers);

        // 创建查询请求
        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setSpecialGoodsCodes(AmazonSpecialTagUtils.getSpecialTagQueryString()); // 所有特供标签
        request.setIsOnline(true); // 在线的listing
        request.setItemTypeList(List.of(AmazonListingitemtypeEnum.Vriant_Item.getStatusCode(), AmazonListingitemtypeEnum.Monomer_Item.getStatusCode()));
        request.setFields(DeleteAmazonListingUtils.ES_AMAZON_PRODUCT_LISTING_FIELDS);
        request.setCustomSaleNumberFileRangeQuery(Triple.of(innerParam.getSaleNumberField(), 0, 0));
        request.setAccountNumberList(accountNumbers);


        // 如果传入了sellerSku列表，则只查询这些sellerSku
        if (innerParam.getSellerSkuList() != null && !innerParam.getSellerSkuList().isEmpty()) {
            request.setSellerSkuList(innerParam.getSellerSkuList());
        }

        // 统计下架数量
        AtomicInteger offlineCount = new AtomicInteger(0);
        Predicate<AmazonAsinSaleCountDO> asinSalePredicate = asinSaleCountDO -> {
            switch (innerParam.getSaleNumberField()) {
                case "order_24H_count":
                    return asinSaleCountDO.getSale_24_count() == null || asinSaleCountDO.getSale_24_count() <= 0;
                case "order_last_7d_count":
                    return asinSaleCountDO.getSale_7d_count() == null || asinSaleCountDO.getSale_7d_count() <= 0;
                case "order_last_14d_count":
                    return asinSaleCountDO.getSale_14d_count() == null || asinSaleCountDO.getSale_14d_count() <= 0;
                case "order_last_30d_count":
                    return asinSaleCountDO.getSale_30d_count() == null || asinSaleCountDO.getSale_30d_count() <= 0;
                case "order_num_total":
                    return asinSaleCountDO.getSale_total_count() == null || asinSaleCountDO.getSale_total_count() <= 0;
                default:
                    return true;
            }
        };

        LocalDateTime endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minusDays(innerParam.getDayRange());


        // 查询符合条件的listing并下架
        int totalCount = esAmazonProductListingService.scrollQueryExecutorTask(request, listingList -> {
            if (listingList != null && !listingList.isEmpty()) {
                // 查询产品系统特殊标签
                List<String> skus = listingList.stream().map(EsAmazonProductListing::getArticleNumber).collect(Collectors.toList());
                List<SingleItemEs> skuSpecialGoodsCodes = singleItemEsService.getSkuSpecialGoodsCode(skus);
                Map<String, List<SpecialGoods>> skuSpecialGoodsCodeMap = skuSpecialGoodsCodes.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getSonSku()))
                        .filter(item -> CollectionUtils.isNotEmpty(item.getSpecialGoodsList()))
                        .collect(Collectors.toMap(SingleItemEs::getSonSku, SingleItemEs::getSpecialGoodsList, (k1, k2) -> k2));

                // 获取ASIN销量
                List<String> sonAsins = listingList.stream().map(EsAmazonProductListing::getSonAsin).collect(Collectors.toList());
                Map<String, AmazonAsinSaleCountDO> asinSaleCountDOMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsins);
                ApiResult<List<AsinSalesVolume>> apiResult = OrderUtils.getAmazonAsinTodaySaleVolume(sonAsins);
                if (!apiResult.isSuccess() || CollectionUtils.isEmpty(apiResult.getResult())) {
                    log.error("获取亚马逊asin销量异常：{}", apiResult.getErrorMsg());
                    return;
                }

                // 合并订单今日销量
                List<AsinSalesVolume> asinSalesVolumes = apiResult.getResult();
                asinSalesVolumes.forEach(asinSalesVolume -> {
                    AmazonAsinSaleCountDO amazonAsinSaleCountDO = asinSaleCountDOMap.get(asinSalesVolume.getAsin());
                    if (amazonAsinSaleCountDO == null) {
                        return;
                    }
                    amazonAsinSaleCountDO.setSale_today_count(Optional.ofNullable(asinSalesVolume.getSalesVolume()).orElse(0));
                });


                List<EsAmazonProductListing> filteredListings = listingList.stream()
                        .filter(listing -> {
                            Date date = Optional.ofNullable(listing.getOpenDate()).orElse(Optional.ofNullable(listing.getFirstOpenDate())
                                    .orElseGet(Date::new));
                            LocalDateTime localDateTime = LocalDateTimeUtil.of(date);
                            return localDateTime.isBefore(endDateTime);
                        })
                        .filter(listing -> {
                            String articleNumber = listing.getArticleNumber();
                            List<SpecialGoods> specialGoods = skuSpecialGoodsCodeMap.get(articleNumber);
                            if (CollectionUtils.isEmpty(specialGoods)) {
                                XxlJobLogger.log("account: {}, sellerSku:{}, sku:{}, listing 特殊标签：{},无特殊标签", listing.getAccountNumber(), listing.getSellerSku(), listing.getArticleNumber(), listing.getSpecialGoodsCode());
                                return false;
                            }

                            boolean anyMatch = specialGoods.stream().anyMatch(specialGood -> AmazonSpecialTagUtils.isAmazonSpecialTag(specialGood.getSpecialType()));
                            if (anyMatch) {
                                return true;
                            }
                            XxlJobLogger.log("account: {}, sellerSku:{}, sku:{},产品系统无特供标签过滤", listing.getAccountNumber(), listing.getSellerSku(), listing.getArticleNumber());
                            return false;
                        })
                        .filter(listing -> specialAccounts.contains(listing.getAccountNumber()))
                        .filter(listing -> {
                            String sonAsin = listing.getSonAsin();
                            AmazonAsinSaleCountDO amazonAsinSaleCountDO = asinSaleCountDOMap.get(sonAsin);
                            if (amazonAsinSaleCountDO != null) {
                                if (!asinSalePredicate.test(amazonAsinSaleCountDO) || amazonAsinSaleCountDO.getSale_today_count() > 0) {
                                    XxlJobLogger.log("account: {}, sellerSku:{}, sku:{},asin:{},销量:{}", listing.getAccountNumber(), listing.getSellerSku(), listing.getArticleNumber(), sonAsin, amazonAsinSaleCountDO);
                                    return false;
                                }
                            }
                            return true;
                        })
                        .collect(Collectors.toList());

                if (!filteredListings.isEmpty()) {
                    List<AmazonProductListing> amazonProductListings = convertToAmazonProductListing(filteredListings);
                    // 创建下架原因
                    DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
                    deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.OFFLINE_EXCLUSIVE_NO_SALE_PRODUCTS);
                    deleteAmazonListingDto.setUseDefaultRemark(true);

                    // 批量下架
                    DeleteAmazonListingUtils.systemBatchRetireProduct(amazonProductListings, deleteAmazonListingDto, null);

                    offlineCount.addAndGet(amazonProductListings.size());
                    XxlJobLogger.log("下架特供标签产品，批次数量: {}", amazonProductListings.size());
                }
            }
        });

        XxlJobLogger.log("特供标签产品无销量下架任务完成，总查询数量: {}，下架数量: {}", totalCount, offlineCount.get());
        return new ReturnT<>(String.format("特供标签产品无销量下架任务完成，总查询数量: %d，下架数量: %d", totalCount, offlineCount.get()));
    }

    /**
     * 将EsAmazonProductListing转换为AmazonProductListing
     */
    private List<AmazonProductListing> convertToAmazonProductListing(List<EsAmazonProductListing> esListings) {
        return esListings.stream().map(esListing -> {
            AmazonProductListing listing = new AmazonProductListing();
            listing.setAccountNumber(esListing.getAccountNumber());
            listing.setSite(esListing.getSite());
            listing.setSellerSku(esListing.getSellerSku());
            listing.setParentAsin(esListing.getParentAsin());
            listing.setSonAsin(esListing.getSonAsin());
            listing.setMainSku(esListing.getMainSku());
            listing.setArticleNumber(esListing.getArticleNumber());
            listing.setIsOnline(esListing.getIsOnline());
            listing.setSkuStatus(esListing.getSkuStatus());
            listing.setSkuDataSource(esListing.getSkuDataSource());
            listing.setItemStatus(esListing.getItemStatus());
            listing.setItemType(esListing.getItemType());
            return listing;
        }).collect(Collectors.toList());
    }
}
