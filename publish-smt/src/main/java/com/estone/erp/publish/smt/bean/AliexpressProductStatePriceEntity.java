package com.estone.erp.publish.smt.bean;


import lombok.Data;

/**
 * <AUTHOR>
 * @description: smt产品32国家
 * @date 2019/11/20 11:45
 */
@Data
public class AliexpressProductStatePriceEntity {

    private String accountNumber;

    private Long productId;

    private String skuId;

    private String skuCode;

    private String priceType;

    private String priceRU;
    private String priceUS;
    private String priceCA;
    private String priceES;
    private String priceFR;
    private String priceUK;
    private String priceNL;
    private String priceIL;
    private String priceBR;
    private String priceCL;
    private String priceAU;
    private String priceUA;
    private String priceBY;
    private String priceJP;
    private String priceTH;
    private String priceSG;
    private String priceKR;
    private String priceID;
    private String priceMY;
    private String pricePH;
    private String priceVN;
    private String priceIT;
    private String priceDE;
    private String priceSA;
    private String priceAE;
    private String pricePL;
    private String priceTR;
    private String pricePT;
    private String priceBE;
    private String priceCO;
    private String priceMX;
    private String priceMA;
    private String priceCH;
    private String priceCZ;
    private String priceNZ;
    private String priceLT;
    private String priceLV;
    private String priceSK;
    private String priceNO;
    private String priceHU;
    private String priceBG;
    private String priceEE;
    private String priceRO;
    private String pricePK;
    private String priceHR;
    private String priceNG;
    private String priceIE;
    private String priceAT;
    private String priceGR;
    private String priceSE;
    private String priceFI;
    private String priceDK;
    private String priceSI;
    private String priceMT;
    private String priceLK;
    private String priceLU;
    private String pricePE;
    private String priceKW;
    private String priceQA;
    private String priceOM;
    private String priceBH;
    private String priceCY;
    private String priceET;
    private String priceUG;
    private String priceZA;
    private String priceKE;
    private String priceGH;
    private String priceDZ;
    // 新增的国家价格字段
    private String priceSRB;  // 塞尔维亚
    private String priceRE;  // 留尼汪岛
    private String priceAO;  // 安哥拉
    private String priceIS;  // 冰岛
    private String priceAL;  // 阿尔巴尼亚
    private String priceMU;  // 毛里求斯
    private String priceMZ;  // 莫桑比克
    private String priceTZ;  // 坦桑尼亚
    private String priceSV;  // 萨尔瓦多
    private String priceMV;  // 马尔代夫
    private String priceCV;  // 佛得角
    private String priceMK;  // 马其顿


    public void setValue(String country, String value) {
        switch(country) {
            case "RU":
                setPriceRU(value);
                break;
            case "US":
                setPriceUS(value);
                break;
            case "CA":
                setPriceCA(value);
                break;
            case "ES":
                setPriceES(value);
                break;
            case "FR":
                setPriceFR(value);
                break;
            case "UK":
                setPriceUK(value);
                break;
            case "NL":
                setPriceNL(value);
                break;
            case "IL":
                setPriceIL(value);
                break;
            case "BR":
                setPriceBR(value);
                break;
            case "CL":
                setPriceCL(value);
                break;
            case "AU":
                setPriceAU(value);
                break;
            case "UA":
                setPriceUA(value);
                break;
            case "BY":
                setPriceBY(value);
                break;
            case "JP":
                setPriceJP(value);
                break;
            case "TH":
                setPriceTH(value);
                break;
            case "SG":
                setPriceSG(value);
                break;
            case "KR":
                setPriceKR(value);
                break;
            case "ID":
                setPriceID(value);
                break;
            case "MY":
                setPriceMY(value);
                break;
            case "PH":
                setPricePH(value);
                break;
            case "VN":
                setPriceVN(value);
                break;
            case "IT":
                setPriceIT(value);
                break;
            case "DE":
                setPriceDE(value);
                break;
            case "SA":
                setPriceSA(value);
                break;
            case "AE":
                setPriceAE(value);
                break;
            case "PL":
                setPricePL(value);
                break;
            case "TR":
                setPriceTR(value);
                break;
            case "PT":
                setPricePT(value);    
                break;
            case "BE":
                setPriceBE(value);
                break;
            case "CO":
                setPriceCO(value);
                break;
            case "MX":
                setPriceMX(value);
                break;
            case "MA":
                setPriceMA(value);
                break;
            case "CH":
                setPriceCH(value);
                break;
            case "CZ":
                setPriceCZ(value);
                break;
            case "NZ":
                setPriceNZ(value);
                break;
            case "LT":
                setPriceLT(value);
                break;
            case "LV":
                setPriceLV(value);
            case "SK":
                setPriceSK(value);
                break;
            case "NO":
                setPriceNO(value);
                break;
            case "HU":
                setPriceHU(value);
                break;
            case "BG":
                setPriceBG(value);
                break;
            case "EE":
                setPriceEE(value);
            case "RO":
                setPriceRO(value);
                break;
            case "PK":
                setPricePK(value);
                break;
            case "HR":
                setPriceHR(value);
                break;
            case "NG":
                setPriceNG(value);
                break;
            case "IE":
                setPriceIE(value);
            case "AT":
                setPriceAT(value);
                break;
            case "GR":
                setPriceGR(value);
                break;
            case "SE":
                setPriceSE(value);
                break;
            case "FI":
                setPriceFI(value);
                break;
            case "DK":
                setPriceDK(value);
            case "SI":
                setPriceSI(value);
                break;
            case "MT":
                setPriceMT(value);
                break;
            case "LK":
                setPriceLK(value);
                break;
            case "LU":
                setPriceLU(value);
                break;
            case "PE":
                setPricePE(value);
                break;
            case "KW":
                setPriceKW(value);
                break;
            case "QA":
                setPriceQA(value);
                break;
            case "OM":
                setPriceOM(value);
                break;
            case "BH":
                setPriceBH(value);
                break;
            case "CY":
                setPriceCY(value);
                break;
            case "ET":
                setPriceET(value);
                break;
            case "UG":
                setPriceUG(value);
                break;
            case "ZA":
                setPriceZA(value);
                break;
            case "KE":
                setPriceKE(value);
                break;
            case "GH":
                setPriceGH(value);
                break;
            case "DZ":
                setPriceDZ(value);
                break;
            // 新增的国家价格设置
            case "SRB":
                setPriceSRB(value);
                break;
            case "RE":
                setPriceRE(value);
                break;
            case "AO":
                setPriceAO(value);
                break;
            case "IS":
                setPriceIS(value);
                break;
            case "AL":
                setPriceAL(value);
                break;
            case "MU":
                setPriceMU(value);
                break;
            case "MZ":
                setPriceMZ(value);
                break;
            case "TZ":
                setPriceTZ(value);
                break;
            case "SV":
                setPriceSV(value);
                break;
            case "MV":
                setPriceMV(value);
                break;
            case "CV":
                setPriceCV(value);
                break;
            case "MK":
                setPriceMK(value);
                break;
        }
    }
}
