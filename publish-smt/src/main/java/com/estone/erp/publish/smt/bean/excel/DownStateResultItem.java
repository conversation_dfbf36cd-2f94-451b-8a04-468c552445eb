package com.estone.erp.publish.smt.bean.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.smt.bean.AliexpressProductStatePriceEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * @Auther lc
 * @Date 2023年7月5日
 */
@Getter
@Setter
public class DownStateResultItem {

    @ExcelProperty(value = "帐号")
    private String account;

    @ExcelProperty(value = "product id")
    private Long productId;

    @ExcelProperty(value = "sku")
    private String sku;

    @ExcelProperty(value = "调价方式")
    private String priceType;

    @ExcelProperty(value = "Russian Federation")
    private String ru;

    @ExcelProperty(value = "United States")
    private String us;

    @ExcelProperty(value = "Canada")
    private String ca;

    @ExcelProperty(value = "Spain")
    private String es;

    @ExcelProperty(value = "France")
    private String fr;

    @ExcelProperty(value = "United Kingdom")
    private String uk;

    @ExcelProperty(value = "Netherlands")
    private String nl;

    @ExcelProperty(value = "Israel")
    private String il;

    @ExcelProperty(value = "Brazil")
    private String br;

    @ExcelProperty(value = "Chile")
    private String cl;

    @ExcelProperty(value = "Australia")
    private String au;

    @ExcelProperty(value = "Ukraine")
    private String ua;

    @ExcelProperty(value = "Belarus")
    private String by;

    @ExcelProperty(value = "Japan")
    private String jp;

    @ExcelProperty(value = "Thailand")
    private String th;

    @ExcelProperty(value = "Singapore")
    private String sg;

    @ExcelProperty(value = "South Korea")
    private String kr;

    @ExcelProperty(value = "Indonesia")
    private String id;

    @ExcelProperty(value = "Malaysia")
    private String my;

    @ExcelProperty(value = "Philippines")
    private String ph;

    @ExcelProperty(value = "Vietnam")
    private String vn;

    @ExcelProperty(value = "Italy")
    private String it;

    @ExcelProperty(value = "Germany")
    private String de;

    @ExcelProperty(value = "Saudi Arabia")
    private String sa;

    @ExcelProperty(value = "United Arab Emirates")
    private String ae;

    @ExcelProperty(value = "Poland")
    private String pl;

    @ExcelProperty(value = "Turkey")
    private String tr;

    @ExcelProperty(value = "Portugal")
    private String pt;

    @ExcelProperty(value = "Belgium")
    private String be;

    @ExcelProperty(value = "Colombia")
    private String co;

    @ExcelProperty(value = "Mexico")
    private String mx;

    @ExcelProperty(value = "Morocco")
    private String ma;

    @ExcelProperty(value = "Switzerland")
    private String ch;

    @ExcelProperty(value = "Czech Republic")
    private String cz;

    @ExcelProperty(value = "New Zealand")
    private String nz;

    @ExcelProperty(value = "Lithuania")
    private String lt;

    @ExcelProperty(value = "Latvia")
    private String lv;

    @ExcelProperty(value = "Slovakia")
    private String sk;

    @ExcelProperty(value = "Norway")
    private String no;

    @ExcelProperty(value = "Hungary")
    private String hu;

    @ExcelProperty(value = "Bulgaria")
    private String bg;

    @ExcelProperty(value = "Estonia")
    private String ee;

    @ExcelProperty(value = "Romania")
    private String ro;

    @ExcelProperty(value = "Pakistan")
    private String pk;

    @ExcelProperty(value = "Croatia")
    private String hr;

    @ExcelProperty(value = "Nigeria")
    private String ng;

    @ExcelProperty(value = "Ireland")
    private String ie;

    @ExcelProperty(value = "Austria")
    private String at;

    @ExcelProperty(value = "Greece")
    private String gr;

    @ExcelProperty(value = "Sweden")
    private String se;

    @ExcelProperty(value = "Finland")
    private String fi;

    @ExcelProperty(value = "Denmark")
    private String dk;

    @ExcelProperty(value = "Slovenia")
    private String si;

    @ExcelProperty(value = "Malta")
    private String mt;

    @ExcelProperty(value = "Sri Lanka")
    private String lk;

    @ExcelProperty(value = "Luxembourg")
    private String lu;

    @ExcelProperty(value = "Peru")
    private String pe;

    @ExcelProperty(value = "Kuwait")
    private String kw;

    @ExcelProperty(value = "Qatar")
    private String qa;

    @ExcelProperty(value = "Oman")
    private String om;

    @ExcelProperty(value = "Bahrain")
    private String bh;

    @ExcelProperty(value = "Cyprus")
    private String cy;

    @ExcelProperty(value = "Ethiopia")
    private String et;

    @ExcelProperty(value = "Uganda")
    private String ug;

    @ExcelProperty(value = "South Africa")
    private String za;

    @ExcelProperty(value = "Kenya")
    private String ke;

    @ExcelProperty(value = "Ghana")
    private String gh;

    @ExcelProperty(value = "Algeria")
    private String dz;

    @ExcelProperty(value = "Serbia")
    private String srb;

    @ExcelProperty(value = "Reunion")
    private String re;

    @ExcelProperty(value = "Angola")
    private String ao;

    @ExcelProperty(value = "Iceland")
    private String is;

    @ExcelProperty(value = "Albania")
    private String al;

    @ExcelProperty(value = "Mauritius")
    private String mu;

    @ExcelProperty(value = "Mozambique")
    private String mz;

    @ExcelProperty(value = "United Republic of Tanzania")
    private String tz;

    @ExcelProperty(value = "El Salvador")
    private String sv;

    @ExcelProperty(value = "Maldives")
    private String mv;

    @ExcelProperty(value = "Cape Verde")
    private String cv;

    @ExcelProperty(value = "North Macedonia")
    private String mk;

    public DownStateResultItem(){
        
    };

    public DownStateResultItem(AliexpressProductStatePriceEntity item){
        this.setAccount(item.getAccountNumber());
        this.setProductId(item.getProductId());
        this.setSku(item.getSkuCode());
        if(item.getPriceType().equals("percentage")) {
            this.setPriceType("百分比");
        }else if(item.getPriceType().equals("relative")) {
            this.setPriceType("基准价");
        }else if(item.getPriceType().equals("absolute")) {
            this.setPriceType("直接调价");
        }else {
            this.setPriceType(item.getPriceType());
        }
        this.setRu(item.getPriceRU());
        this.setUs(item.getPriceUS());
        this.setCa(item.getPriceCA());
        this.setEs(item.getPriceES());
        this.setFr(item.getPriceFR());
        this.setUk(item.getPriceUK());
        this.setNl(item.getPriceNL());
        this.setIl(item.getPriceIL());
        this.setBr(item.getPriceBR());
        this.setCl(item.getPriceCL());
        this.setAu(item.getPriceAU());
        this.setUa(item.getPriceUA());
        this.setBy(item.getPriceBY());
        this.setJp(item.getPriceJP());
        this.setTh(item.getPriceTH());
        this.setSg(item.getPriceSG());
        this.setKr(item.getPriceKR());
        this.setId(item.getPriceID());
        this.setMy(item.getPriceMY());
        this.setPh(item.getPricePH());
        this.setVn(item.getPriceVN());
        this.setIt(item.getPriceIT());
        this.setDe(item.getPriceDE());
        this.setSa(item.getPriceSA());
        this.setAe(item.getPriceAE());
        this.setPl(item.getPricePL());
        this.setTr(item.getPriceTR());
        this.setPt(item.getPricePT());
        this.setBe(item.getPriceBE());
        this.setCo(item.getPriceCO());
        this.setMx(item.getPriceMX());
        this.setMa(item.getPriceMA());
        this.setCh(item.getPriceCH());
        this.setCz(item.getPriceCZ());
        this.setNz(item.getPriceNZ());
        this.setLt(item.getPriceLT());
        this.setLv(item.getPriceLV());
        this.setSk(item.getPriceSK());
        this.setNo(item.getPriceNO());
        this.setHu(item.getPriceHU());
        this.setBg(item.getPriceBG());
        this.setEe(item.getPriceEE());
        this.setRo(item.getPriceRO());
        this.setPk(item.getPricePK());
        this.setHr(item.getPriceHR());
        this.setNg(item.getPriceNG());
        this.setIe(item.getPriceIE());
        this.setAt(item.getPriceAT());
        this.setGr(item.getPriceGR());
        this.setSe(item.getPriceSE());
        this.setFi(item.getPriceFI());
        this.setDk(item.getPriceDK());
        this.setSi(item.getPriceSI());
        this.setMt(item.getPriceMT());
        this.setLk(item.getPriceLK());
        this.setLu(item.getPriceLU());
        this.setPe(item.getPricePE());
        this.setKw(item.getPriceKW());
        this.setQa(item.getPriceQA());
        this.setOm(item.getPriceOM());
        this.setBh(item.getPriceBH());
        this.setCy(item.getPriceCY());
        this.setEt(item.getPriceET());
        this.setUg(item.getPriceUG());
        this.setZa(item.getPriceZA());
        this.setKe(item.getPriceKE());
        this.setGh(item.getPriceGH());
        this.setDz(item.getPriceDZ());
        // 新增的国家字段设置
        this.setSrb(item.getPriceSRB());  // 塞尔维亚
        this.setRe(item.getPriceRE());  // 留尼汪岛
        this.setAo(item.getPriceAO());  // 安哥拉
        this.setIs(item.getPriceIS());  // 冰岛
        this.setAl(item.getPriceAL());  // 阿尔巴尼亚
        this.setMu(item.getPriceMU());  // 毛里求斯
        this.setMz(item.getPriceMZ());  // 莫桑比克
        this.setTz(item.getPriceTZ());  // 坦桑尼亚
        this.setSv(item.getPriceSV());  // 萨尔瓦多
        this.setMv(item.getPriceMV());  // 马尔代夫
        this.setCv(item.getPriceCV());  // 佛得角
        this.setMk(item.getPriceMK());  // 马其顿
    }

}
