package com.estone.erp.publish.smt.mq.excel.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.MapUtil;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.component.EasyExeclListener;
import com.estone.erp.publish.smt.bean.AliexpressEditProductBean;
import com.estone.erp.publish.smt.bean.UpdatePriceEntity;
import com.estone.erp.publish.smt.bean.excel.DownStateItem;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.mq.excel.ExcelAbstract;
import com.estone.erp.publish.smt.mq.excel.bean.ExcelBean;
import com.estone.erp.publish.smt.mq.excel.config.ExcelContext;
import com.estone.erp.publish.smt.mq.excel.constant.ExcelConstant;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/1/1110:54
 */
@Slf4j
public class ExcelStatePriceEasyImpl extends ExcelAbstract {

    private AliexpressEsExtendService aliexpressEsExtendService = SpringUtils.getBean(AliexpressEsExtendService.class);

    @Override
    public
    ResponseJson execute(ExcelContext context) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        ExcelBean excelBean = context.getExcelBean();
        String priceType = excelBean.getPriceType();
        String uploadUrl = excelBean.getUploadUrl();

        // 获取文件流 easyexel 直接使用文件路径有异常错误地址
        InputStream ins = null;
        try{
            URL url = new URL(uploadUrl);
            URLConnection uri = url.openConnection();
            //获取数据流
            ins = uri.getInputStream();
        }catch (Exception e){
            throw new RuntimeException("文件系统获取文件流失败" + e.getMessage());
        }
        try {
            // 分批边读取数据 边处理数据
            int limit = 1000;
            EasyExeclListener.BATCH_COUNT = limit;

            List<UpdatePriceEntity> allReturnResultList = new ArrayList<>();

            //每次分页的头尾数据
            List<DownStateItem> finalList = new ArrayList<>();

            EasyExcel.read(ins, DownStateItem.class, new EasyExeclListener<DownStateItem>(dataList ->{
                try {
                    //第一个产品数据
                    Long firstProductId = dataList.get(0).getProductId();
                    List<DownStateItem> firstDataList = dataList.stream().filter(t -> t.getProductId().longValue() == firstProductId.longValue()).collect(Collectors.toList());
                    finalList.addAll(firstDataList);

                    //保留最后一个产品id的数据  如果是改一个产品的数据，会相同
                    Long lastProductId = dataList.get(dataList.size() - 1).getProductId();
                    if(firstProductId.longValue() != lastProductId.longValue()){
                        List<DownStateItem> lastDataList = dataList.stream().filter(t -> t.getProductId().longValue() == lastProductId.longValue()).collect(Collectors.toList());
                        finalList.addAll(lastDataList);
                    }

                    //中间数据
                    List<DownStateItem> middleDataList = dataList.stream().filter(t -> t.getProductId().longValue() != firstProductId.longValue() && t.getProductId().longValue() != lastProductId.longValue()).collect(Collectors.toList());

                    if(CollectionUtils.isNotEmpty(middleDataList)){
                        //整合修改数据
                        Map<String, List<AliexpressEditProductBean>> excelDataList = new HashMap<>();
                        for (DownStateItem downStateItem : middleDataList) {
                            Long productId = downStateItem.getProductId();
                            String account = downStateItem.getAccount();
                            if(StringUtils.isBlank(account) || productId == null){
                                continue;
                            }

                            List<AliexpressEditProductBean> aliexpressEditProductBeans = excelDataList.get(productId.toString());
                            if(CollectionUtils.isEmpty(aliexpressEditProductBeans)){
                                aliexpressEditProductBeans = new ArrayList<>();
                            }
                            AliexpressEditProductBean editProductBean = new AliexpressEditProductBean();
                            editProductBean.setAccountNum(account);
                            editProductBean.setProductId(productId.toString());
                            editProductBean.setSkuCode(downStateItem.getArticleNumber());
                            editProductBean.setSkuId(downStateItem.getSkuId());
                            editProductBean.setPriceType(priceType);
                            Map<String, Double> countryPriceMap = getCountryPrice(downStateItem);
                            editProductBean.setCountryPriceMap(countryPriceMap);
                            aliexpressEditProductBeans.add(editProductBean);
                            excelDataList.put(productId.toString(), aliexpressEditProductBeans);
                        }

                        if(MapUtil.isNotEmpty(excelDataList) && excelDataList.size() > 0){
                            List<UpdatePriceEntity> returnResultList = aliexpressEsExtendService.updateProductCountryPriceNew(excelDataList, excelBean.getCreateBy(), null, true);
                            allReturnResultList.addAll(returnResultList);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            })).sheet().doRead();

            if(CollectionUtils.isNotEmpty(finalList)){
                //整合修改数据
                Map<String, List<AliexpressEditProductBean>> excelDataList = new HashMap<>();
                for (DownStateItem downStateItem : finalList) {
                    Long productId = downStateItem.getProductId();
                    String account = downStateItem.getAccount();
                    if(StringUtils.isBlank(account) || productId == null){
                        continue;
                    }

                    List<AliexpressEditProductBean> aliexpressEditProductBeans = excelDataList.get(productId.toString());
                    if(CollectionUtils.isEmpty(aliexpressEditProductBeans)){
                        aliexpressEditProductBeans = new ArrayList<>();
                    }
                    AliexpressEditProductBean editProductBean = new AliexpressEditProductBean();
                    editProductBean.setAccountNum(account);
                    editProductBean.setProductId(productId.toString());
                    editProductBean.setSkuCode(downStateItem.getArticleNumber());
                    editProductBean.setSkuId(downStateItem.getSkuId());
                    editProductBean.setPriceType(priceType);
                    Map<String, Double> countryPriceMap = getCountryPrice(downStateItem);
                    editProductBean.setCountryPriceMap(countryPriceMap);
                    aliexpressEditProductBeans.add(editProductBean);
                    excelDataList.put(productId.toString(), aliexpressEditProductBeans);
                }
                if(MapUtil.isNotEmpty(excelDataList) && excelDataList.size() > 0){
                    List<UpdatePriceEntity> returnResultList = aliexpressEsExtendService.updateProductCountryPriceNew(excelDataList, excelBean.getCreateBy(), null, true);
                    allReturnResultList.addAll(returnResultList);
                }
            }

            Set<String> accountSet = new HashSet<>();
            for (UpdatePriceEntity updatePriceEntity : allReturnResultList) {
                if(accountSet.size() < 50){
                    accountSet.add(updatePriceEntity.getSeller());
                }
            }

            List<String> includeColumnFiledNames = Arrays.asList("seller", "productId", "skuCode", "errorTip");

            String fileName = "down_" + System.currentTimeMillis() + "";
            String suffix = ".xlsx";
            File file = File.createTempFile(fileName, suffix);
            EasyExcel.write(file, UpdatePriceEntity.class)
                    .includeColumnFiledNames(includeColumnFiledNames)
                    .sheet("sheet")
                    .doWrite(allReturnResultList);

            rsp.getBody().put(ExcelConstant.itemCountConstant, allReturnResultList.size());
            rsp.getBody().put(ExcelConstant.accountStrConstant, "," + StringUtils.join(accountSet, ",") + ",");

            if(file.length() > 0){
                ApiResult<SeaweedFile> uploadResult = FmsApiUtil
                        .publishFileUpload(file, fileName, ExcelConstant.smtExcel, DataContextHolder.getUsername());
                boolean delete = file.delete();
                if(!delete){
                    log.error("异常:" + delete);
                }
                if(uploadResult.isSuccess()) {
                    SeaweedFile result = uploadResult.getResult();
                    if (null != result) {
                        String url2 = result.getUrl2();
                        if (StringUtils.isNotBlank(url2)) {
                            //路径
                            rsp.setStatus(StatusCode.SUCCESS);
                            rsp.setMessage(url2);
                        }
                        if(!StringUtils.equalsIgnoreCase(StatusCode.SUCCESS, rsp.getStatus()) && StringUtils.isBlank(rsp.getMessage())){
                            rsp.setMessage(JSON.toJSONString(result));
                        }
                    }
                }else {
                    rsp.setMessage(uploadResult.getErrorMsg());
                }
            }else{
                rsp.setMessage("生成Excel失败");
            }
        } catch (Exception e) {
            log.error("生成excel结果异常:" + e.getMessage(), e);
            rsp.setMessage("生成excel结果异常:" + e.getMessage());
        }
        return rsp;
    }

    public static Map<String, Double> getCountryPrice(DownStateItem downStateItem){
        Map<String, Double> countryPriceMap = new HashMap<>();
        if(StringUtils.isNotBlank(downStateItem.getRu())){
            countryPriceMap.put("RU", Double.parseDouble(downStateItem.getRu()));
        }
        if(StringUtils.isNotBlank(downStateItem.getUs())){
            countryPriceMap.put("US", Double.parseDouble(downStateItem.getUs()));
        }
        if(StringUtils.isNotBlank(downStateItem.getCa())){
            countryPriceMap.put("CA", Double.parseDouble(downStateItem.getCa()));
        }
        if(StringUtils.isNotBlank(downStateItem.getEs())){
            countryPriceMap.put("ES", Double.parseDouble(downStateItem.getEs()));
        }
        if(StringUtils.isNotBlank(downStateItem.getFr())){
            countryPriceMap.put("FR", Double.parseDouble(downStateItem.getFr()));
        }
        if (StringUtils.isNotBlank(downStateItem.getUk())) {
            countryPriceMap.put("UK", Double.parseDouble(downStateItem.getUk()));
        }
        if(StringUtils.isNotBlank(downStateItem.getNl())){
            countryPriceMap.put("NL", Double.parseDouble(downStateItem.getNl()));
        }
        if(StringUtils.isNotBlank(downStateItem.getIl())){
            countryPriceMap.put("IL", Double.parseDouble(downStateItem.getIl()));
        }
        if(StringUtils.isNotBlank(downStateItem.getBr())){
            countryPriceMap.put("BR", Double.parseDouble(downStateItem.getBr()));
        }
        if(StringUtils.isNotBlank(downStateItem.getCl())){
            countryPriceMap.put("CL", Double.parseDouble(downStateItem.getCl()));
        }
        if(StringUtils.isNotBlank(downStateItem.getAu())){
            countryPriceMap.put("AU", Double.parseDouble(downStateItem.getAu()));
        }
        if(StringUtils.isNotBlank(downStateItem.getUa())){
            countryPriceMap.put("UA", Double.parseDouble(downStateItem.getUa()));
        }
        if(StringUtils.isNotBlank(downStateItem.getBy())){
            countryPriceMap.put("BY", Double.parseDouble(downStateItem.getBy()));
        }
        if(StringUtils.isNotBlank(downStateItem.getJp())){
            countryPriceMap.put("JP", Double.parseDouble(downStateItem.getJp()));
        }
        if(StringUtils.isNotBlank(downStateItem.getTh())){
            countryPriceMap.put("TH", Double.parseDouble(downStateItem.getTh()));
        }
        if(StringUtils.isNotBlank(downStateItem.getSg())){
            countryPriceMap.put("SG", Double.parseDouble(downStateItem.getSg()));
        }
        if(StringUtils.isNotBlank(downStateItem.getKr())){
            countryPriceMap.put("KR", Double.parseDouble(downStateItem.getKr()));
        }
        if(StringUtils.isNotBlank(downStateItem.getId())){
            countryPriceMap.put("ID", Double.parseDouble(downStateItem.getId()));
        }
        if(StringUtils.isNotBlank(downStateItem.getMy())){
            countryPriceMap.put("MY", Double.parseDouble(downStateItem.getMy()));
        }
        if(StringUtils.isNotBlank(downStateItem.getPh())){
            countryPriceMap.put("PH", Double.parseDouble(downStateItem.getPh()));
        }
        if(StringUtils.isNotBlank(downStateItem.getVn())){
            countryPriceMap.put("VN", Double.parseDouble(downStateItem.getVn()));
        }
        if(StringUtils.isNotBlank(downStateItem.getIt())){
            countryPriceMap.put("IT", Double.parseDouble(downStateItem.getIt()));
        }
        if(StringUtils.isNotBlank(downStateItem.getDe())){
            countryPriceMap.put("DE", Double.parseDouble(downStateItem.getDe()));
        }
        if(StringUtils.isNotBlank(downStateItem.getSa())){
            countryPriceMap.put("SA", Double.parseDouble(downStateItem.getSa()));
        }
        if(StringUtils.isNotBlank(downStateItem.getAe())){
            countryPriceMap.put("AE", Double.parseDouble(downStateItem.getAe()));
        }
        if(StringUtils.isNotBlank(downStateItem.getPl())){
            countryPriceMap.put("PL", Double.parseDouble(downStateItem.getPl()));
        }
        if(StringUtils.isNotBlank(downStateItem.getTr())){
            countryPriceMap.put("TR", Double.parseDouble(downStateItem.getTr()));
        }
        if(StringUtils.isNotBlank(downStateItem.getPt())){
            countryPriceMap.put("PT", Double.parseDouble(downStateItem.getPt()));
        }
        if(StringUtils.isNotBlank(downStateItem.getBe())){
            countryPriceMap.put("BE", Double.parseDouble(downStateItem.getBe()));
        }
        if(StringUtils.isNotBlank(downStateItem.getCo())){
            countryPriceMap.put("CO", Double.parseDouble(downStateItem.getCo()));
        }
        if(StringUtils.isNotBlank(downStateItem.getMx())){
            countryPriceMap.put("MX", Double.parseDouble(downStateItem.getMx()));
        }
        if(StringUtils.isNotBlank(downStateItem.getMa())){
            countryPriceMap.put("MA", Double.parseDouble(downStateItem.getMa()));
        }
        if(StringUtils.isNotBlank(downStateItem.getCh())){
            countryPriceMap.put("CH", Double.parseDouble(downStateItem.getCh()));
        }
        if(StringUtils.isNotBlank(downStateItem.getCz())){
            countryPriceMap.put("CZ", Double.parseDouble(downStateItem.getCz()));
        }
        if(StringUtils.isNotBlank(downStateItem.getNz())){
            countryPriceMap.put("NZ", Double.parseDouble(downStateItem.getNz()));
        }
        if(StringUtils.isNotBlank(downStateItem.getLt())){
            countryPriceMap.put("LT", Double.parseDouble(downStateItem.getLt()));
        }
        if(StringUtils.isNotBlank(downStateItem.getLv())){
            countryPriceMap.put("LV", Double.parseDouble(downStateItem.getLv()));
        }
        if(StringUtils.isNotBlank(downStateItem.getSk())){
            countryPriceMap.put("SK", Double.parseDouble(downStateItem.getSk()));
        }
        if(StringUtils.isNotBlank(downStateItem.getNo())){
            countryPriceMap.put("NO", Double.parseDouble(downStateItem.getNo()));
        }
        if(StringUtils.isNotBlank(downStateItem.getHu())){
            countryPriceMap.put("HU", Double.parseDouble(downStateItem.getHu()));
        }
        if(StringUtils.isNotBlank(downStateItem.getBg())){
            countryPriceMap.put("BG", Double.parseDouble(downStateItem.getBg()));
        }
        if(StringUtils.isNotBlank(downStateItem.getEe())){
            countryPriceMap.put("EE", Double.parseDouble(downStateItem.getEe()));
        }
        if(StringUtils.isNotBlank(downStateItem.getRo())){
            countryPriceMap.put("RO", Double.parseDouble(downStateItem.getRo()));
        }
        if(StringUtils.isNotBlank(downStateItem.getPk())){
            countryPriceMap.put("PK", Double.parseDouble(downStateItem.getPk()));
        }
        if(StringUtils.isNotBlank(downStateItem.getHr())){
            countryPriceMap.put("HR", Double.parseDouble(downStateItem.getHr()));
        }
        if(StringUtils.isNotBlank(downStateItem.getNg())){
            countryPriceMap.put("NG", Double.parseDouble(downStateItem.getNg()));
        }
        if(StringUtils.isNotBlank(downStateItem.getIe())){
            countryPriceMap.put("IE", Double.parseDouble(downStateItem.getIe()));
        }
        if(StringUtils.isNotBlank(downStateItem.getAt())){
            countryPriceMap.put("AT", Double.parseDouble(downStateItem.getAt()));
        }
        if(StringUtils.isNotBlank(downStateItem.getGr())){
            countryPriceMap.put("GR", Double.parseDouble(downStateItem.getGr()));
        }
        if(StringUtils.isNotBlank(downStateItem.getSe())){
            countryPriceMap.put("SE", Double.parseDouble(downStateItem.getSe()));
        }
        if(StringUtils.isNotBlank(downStateItem.getFi())){
            countryPriceMap.put("FI", Double.parseDouble(downStateItem.getFi()));
        }
        if(StringUtils.isNotBlank(downStateItem.getDk())){
            countryPriceMap.put("DK", Double.parseDouble(downStateItem.getDk()));
        }
        if(StringUtils.isNotBlank(downStateItem.getSi())){
            countryPriceMap.put("SI", Double.parseDouble(downStateItem.getSi()));
        }
        if(StringUtils.isNotBlank(downStateItem.getMt())){
            countryPriceMap.put("MT", Double.parseDouble(downStateItem.getMt()));
        }
        if(StringUtils.isNotBlank(downStateItem.getLk())){
            countryPriceMap.put("LK", Double.parseDouble(downStateItem.getLk()));
        }
        if(StringUtils.isNotBlank(downStateItem.getLu())){
            countryPriceMap.put("LU", Double.parseDouble(downStateItem.getLu()));
        }
        if(StringUtils.isNotBlank(downStateItem.getPe())){
            countryPriceMap.put("PE", Double.parseDouble(downStateItem.getPe()));
        }
        if(StringUtils.isNotBlank(downStateItem.getKw())){
            countryPriceMap.put("KW",Double.parseDouble(downStateItem.getKw()));
        }
        if(StringUtils.isNotBlank(downStateItem.getQa())){
            countryPriceMap.put("QA",Double.parseDouble(downStateItem.getQa()));
        }
        if(StringUtils.isNotBlank(downStateItem.getOm())){
            countryPriceMap.put("OM",Double.parseDouble(downStateItem.getOm()));
        }
        if(StringUtils.isNotBlank(downStateItem.getBh())){
            countryPriceMap.put("BH",Double.parseDouble(downStateItem.getBh()));
        }
        if(StringUtils.isNotBlank(downStateItem.getCy())){
            countryPriceMap.put("CY",Double.parseDouble(downStateItem.getCy()));
        }
        if(StringUtils.isNotBlank(downStateItem.getEt())){
            countryPriceMap.put("ET",Double.parseDouble(downStateItem.getEt()));
        }
        //UG
        if(StringUtils.isNotBlank(downStateItem.getUg())){
            countryPriceMap.put("UG",Double.parseDouble(downStateItem.getUg()));
        }
        //South Africa
        if(StringUtils.isNotBlank(downStateItem.getZa())){
            countryPriceMap.put("ZA",Double.parseDouble(downStateItem.getZa()));
        }
        //Kenya
        if(StringUtils.isNotBlank(downStateItem.getKe())){
            countryPriceMap.put("KE",Double.parseDouble(downStateItem.getKe()));
        }
        //Ghana
        if(StringUtils.isNotBlank(downStateItem.getGh())){
            countryPriceMap.put("GH",Double.parseDouble(downStateItem.getGh()));
        }
        //Algeria
        if(StringUtils.isNotBlank(downStateItem.getDz())){
            countryPriceMap.put("DZ",Double.parseDouble(downStateItem.getDz()));
        }
        // 塞尔维亚
        if (StringUtils.isNotBlank(downStateItem.getSrb())) {
            countryPriceMap.put("SRB", Double.parseDouble(downStateItem.getSrb()));
        }
        // 留尼汪岛
        if (StringUtils.isNotBlank(downStateItem.getRe())) {
            countryPriceMap.put("RE", Double.parseDouble(downStateItem.getRe()));
        }
        // 安哥拉
        if (StringUtils.isNotBlank(downStateItem.getAo())) {
            countryPriceMap.put("AO", Double.parseDouble(downStateItem.getAo()));
        }
        // 冰岛
        if (StringUtils.isNotBlank(downStateItem.getIs())) {
            countryPriceMap.put("IS", Double.parseDouble(downStateItem.getIs()));
        }
        // 阿尔巴尼亚
        if (StringUtils.isNotBlank(downStateItem.getAl())) {
            countryPriceMap.put("AL", Double.parseDouble(downStateItem.getAl()));
        }
        // 毛里求斯
        if (StringUtils.isNotBlank(downStateItem.getMu())) {
            countryPriceMap.put("MU", Double.parseDouble(downStateItem.getMu()));
        }
        // 莫桑比克
        if (StringUtils.isNotBlank(downStateItem.getMz())) {
            countryPriceMap.put("MZ", Double.parseDouble(downStateItem.getMz()));
        }
        // 坦桑尼亚
        if (StringUtils.isNotBlank(downStateItem.getTz())) {
            countryPriceMap.put("TZ", Double.parseDouble(downStateItem.getTz()));
        }
        // 萨尔瓦多
        if (StringUtils.isNotBlank(downStateItem.getSv())) {
            countryPriceMap.put("SV", Double.parseDouble(downStateItem.getSv()));
        }
        // 马尔代夫
        if (StringUtils.isNotBlank(downStateItem.getMv())) {
            countryPriceMap.put("MV", Double.parseDouble(downStateItem.getMv()));
        }
        // 佛得角
        if (StringUtils.isNotBlank(downStateItem.getCv())) {
            countryPriceMap.put("CV", Double.parseDouble(downStateItem.getCv()));
        }
        // 北马其顿
        if (StringUtils.isNotBlank(downStateItem.getMk())) {
            countryPriceMap.put("MK", Double.parseDouble(downStateItem.getMk()));
        }
        return countryPriceMap;
    }

    @Override
    public int type() {
        return ExcelTypeEnum.statePrice.getCode();
    }
}
