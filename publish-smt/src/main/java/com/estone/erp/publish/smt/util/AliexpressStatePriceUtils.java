package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.shaded.com.google.common.base.Predicate;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.NumberUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.ExcelUtils;
import com.estone.erp.publish.common.util.POIUtils;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.smt.bean.*;
import com.estone.erp.publish.smt.call.direct.FreightTemplateOpenCall;
import com.estone.erp.publish.smt.call.direct.utils.PreCheckUtils;
import com.estone.erp.publish.smt.call.direct.v2.OfferQueryProductOpenCall;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.smt.service.AliexpressFreightTemplateService;
import com.estone.erp.publish.smt.service.AliexpressProductForAreaPriceService;
import com.estone.erp.publish.smt.template.attribute.AliexpressTemplateAttributeHelper;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.groovy.util.Maps;
import org.apache.poi.ss.usermodel.Cell;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Slf4j
public class AliexpressStatePriceUtils {

    public static final String isMySql = "isMySql";

    public static final String[] headerState = { "销售组长", "销售", "帐号", "product id", "标题", "货号", "sku", "价格", "库存", "商品状态",
            "毛利", "毛利率", "店铺分组", "运费分组", "SKU的重量+填充物重量+搭配包材重量", "SKU销售成本价", "SKU对应的包材重量", "包材价格+填充物价格",
            "产品在SMT后台填写的重量", "单品状态", "禁售平台", "产品标签", "类目id", "sku_id",
            "调价方式","Russian Federation","United States","Canada","Spain",
            "France","United Kingdom","Netherlands","Israel","Brazil",
            "Chile","Australia","Ukraine","Belarus","Japan","Thailand","Singapore",
            "South Korea","Indonesia","Malaysia","Philippines", "Vietnam","Italy",
            "Germany","Saudi Arabia","United Arab Emirates","Poland","Turkey","Portugal",
            "Belgium", "Colombia", "Mexico", "Morocco",
            "Switzerland","Czech Republic","New Zealand","Lithuania","Latvia",
            "Slovakia","Norway","Hungary","Bulgaria","Estonia",
            "Romania","Pakistan","Croatia","Nigeria","Ireland",
            "Austria","Greece","Sweden","Finland","Denmark",
            "Slovenia","Malta","Sri Lanka","Luxembourg","Peru","Kuwait",
            "Qatar","Oman","Bahrain","Cyprus","Ethiopia","Uganda",
            "South Africa", "Kenya", "Ghana", "Algeria",
            "Serbia", "Reunion", "Angola", "Iceland", "Albania",
            "Mauritius", "Mozambique", "United Republic of Tanzania", "El Salvador",
            "Maldives", "Cape Verde", "North Macedonia"


    };

    public final static String[] countryList = {
            "RU", "US", "CA", "ES", "FR", "UK", "NL", "IL",
            "BR", "CL", "AU", "UA", "BY", "JP", "TH", "SG",
            "KR", "ID", "MY", "PH", "VN", "IT", "DE", "SA",
            "AE", "PL", "TR", "PT", "BE", "CO", "MX", "MA",
            "CH", "CZ", "NZ", "LT", "LV", "SK", "NO", "HU",
            "BG", "EE", "RO", "PK", "HR", "NG", "IE", "AT",
            "GR", "SE", "FI", "DK", "SI", "MT", "LK", "LU",
            "PE", "KW", "QA", "OM", "BH", "CY", "ET", "UG",
            "ZA", "KE", "GH", "DZ", "SRB", "RE", "AO", "IS", "AL", "MU", "MZ", "TZ", "SV", "MV", "CV", "MK"

    };

    public final static Map<String, String> countryCodeAndCnName = Maps.of(
            "RU", "俄罗斯", "US", "美国", "CA", "加拿大", "ES", "西班牙",
            "FR", "法国", "UK", "英国", "NL", "荷兰", "IL", "以色列",
            "BR", "巴西", "CL", "智利", "AU", "澳大利亚", "UA", "乌克兰",
            "BY", "白俄罗斯", "JP", "日本", "TH", "泰国", "SG", "新加坡",
            "KR", "韩国", "ID", "印度尼西亚", "MY", "马来西亚", "PH", "菲律宾",
            "VN", "越南", "IT", "意大利", "DE", "德国", "SA", "沙特阿拉伯",
            "AE", "阿联酋", "PL", "波兰", "TR", "土耳其", "PT", "葡萄牙",
            "BE", "比利时", "CO", "哥伦比亚", "MX", "墨西哥", "MA", "摩洛哥",
            "CH", "瑞士", "CZ", "捷克", "NZ", "新西兰", "LT", "立陶宛",
            "LV", "拉脱维亚", "SK", "斯洛伐克", "NO", "挪威", "HU", "匈牙利",
            "BG", "保加利亚", "EE", "爱沙尼亚", "RO", "罗马尼亚", "PK", "巴基斯坦",
            "HR", "克罗地亚", "NG", "尼日利亚", "IE", "爱尔兰", "AT", "奥地利",
            "GR", "希腊", "SE", "瑞典", "FI", "芬兰", "DK", "丹麦",
            "SI", "斯洛文尼亚", "MT", "马耳他", "LK", "斯里兰卡", "LU", "卢森堡",
            "PE", "秘鲁", "KW", "科威特", "QA", "卡塔尔", "OM", "阿曼",
            "BH", "巴林", "CY", "塞浦路斯", "ET", "埃塞俄比亚", "UG", "乌干达",
            "ZA", "南非", "KE", "肯尼亚", "GH", "加纳", "DZ", "阿尔及利亚",
            "SRB", "塞尔维亚", "RE", "留尼汪岛", "AO", "安哥拉", "IS", "冰岛",
            "AL", "阿尔巴尼亚", "MU", "毛里求斯", "MZ", "莫桑比克", "TZ", "坦桑尼亚",
            "SV", "萨尔瓦多", "MV", "马尔代夫", "CV", "佛得角", "MK", "马其顿"
    );

    public static AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);
    public static EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
    public static AliexpressEsExtendService aliexpressEsExtendService = SpringUtils.getBean(AliexpressEsExtendService.class);
    public static SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
    public static AliexpressTemplateAttributeHelper aliexpressTemplateAttributeHelper = SpringUtils.getBean(AliexpressTemplateAttributeHelper.class);
    public static AliexpressHalfTgItemService aliexpressHalfTgItemService = SpringUtils.getBean(AliexpressHalfTgItemService.class);
    public static AliexpressFreightTemplateService aliexpressFreightTemplateService = SpringUtils.getBean(AliexpressFreightTemplateService.class);
    public static AliexpressProductForAreaPriceService aliexpressProductForAreaPriceService = SpringUtils.getBean(AliexpressProductForAreaPriceService.class);

    /**
     * 配置参数aeop_national_quote_configuration
     * @param item
     * @param quoteConfigJson
     * @return
     */
    public static JSONObject transQuote(AliexpressEditProductBean item, JSONObject quoteConfigJson) {
        String aliexpressAccountNumber = item.getAccountNum();
        String productId = item.getProductId();
        String skuId = item.getSkuId();

        //如果是海外仓产品，就不需要组装
        EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setAliexpressAccountNumber(aliexpressAccountNumber);
        request.setProductId(Long.valueOf(productId));
        request.setSkuId(skuId);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);
        if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
            Boolean isOverseas = esAliexpressProductListing.get(0).getIsOverseas();
            if(null != isOverseas && isOverseas){
                return quoteConfigJson;
            }
        }

        String priceType = item.getPriceType();
        Map<String, Double> countryPriceMap = item.getCountryPriceMap();
        try {
            List<JSONObject> newQuoteList = new ArrayList<JSONObject>();
            if(null != quoteConfigJson) {
                List<JSONObject> quoteList = JSON.parseObject(quoteConfigJson.getString("configuration_data"),new TypeReference<List<JSONObject>>(){});
                Map<String, Map<String, Double>> configDataMap = new HashMap<>();
                for (JSONObject data : quoteList) {
                    configDataMap.put(data.getString("shiptoCountry"), JSON.parseObject(data.getString("absoluteQuoteMap"), new TypeReference<Map<String, Double>>(){}));
                }
                for(int i = 0; i < countryList.length; i++) {
                    JSONObject nation = new JSONObject();
                    String country = countryList[i];
                    nation.put("shiptoCountry", country);
//                    if(countryPriceMap.containsKey(country)) {
                        Double price = countryPriceMap.get(country);
                        Map<String, Double> quote = configDataMap.get(country);
                        if(price == null){
                            continue;
                        }
                        if(skuId.equals("<none>")) {// 单sku
                            quote.put("", price);
                        }else {// 多sku
                            quote.put(skuId, price);
                        }
                        nation.put("absoluteQuoteMap", quote);
                        newQuoteList.add(nation);
//                    }
                }
            }else {
                quoteConfigJson = new JSONObject();
                quoteConfigJson.put("configuration_type", priceType);
                for(int i = 0; i < countryList.length; i++) {
                    JSONObject nation = new JSONObject();
                    String country = countryList[i];
                    nation.put("shiptoCountry", country);
//                    if(countryPriceMap.containsKey(country)) {
                        Double price = countryPriceMap.get(country);
                        Map<String, Double> quote = new HashMap<String, Double>();
                        if(price == null){
                            continue;
                        }
                        if(skuId.equals("<none>")) {// 单sku
                            quote.put("", price);
                        }else {// 多sku
                            quote.put(skuId, price);
                        }
                        nation.put("absoluteQuoteMap", quote);
                        newQuoteList.add(nation);
//                    }
                }
            }
            quoteConfigJson.put("configuration_data", newQuoteList);
            return quoteConfigJson;
        }catch (Exception e) {
            log.error("修改失败-组装国家价格失败【aliexpressAccountNumber：" + aliexpressAccountNumber + "，productId：" + productId + "】");
        }
        return quoteConfigJson;
    }

    /**
     * 将同步回来的最新产品实体
     * 转换成用于更新产品信息的实体
     * @param syncResult
     * @return
     */
    public static JSONObject transResultToUpdate(String syncResult, SaleAccountAndBusinessResponse saleAccountByAccountNumber) {
        if(StringUtils.isNotBlank(syncResult)) {
            JSONObject object = JSONObject.parseObject(syncResult);
            if (object.containsKey("aliexpress_postproduct_redefining_findaeproductbyid_response")) {
                JSONObject productRsp = object
                        .getJSONObject("aliexpress_postproduct_redefining_findaeproductbyid_response");
                if (productRsp.containsKey("result")) {
                    JSONObject obj = productRsp.getJSONObject("result");
                    JSONObject updateObj = new JSONObject();
                    updateObj.put("bulk_order", obj.get("bulk_order"));
                    updateObj.put("lot_num", obj.get("lot_num"));
                    updateObj.put("summary", obj.get("summary"));
                    if(obj.containsKey("aeop_ae_product_s_k_us")) {
                        JSONObject resuleSkus = obj.getJSONObject("aeop_ae_product_s_k_us");
                        JSONArray resuleSkuArray = resuleSkus.getJSONArray("aeop_ae_product_sku");
                        JSONArray skus = new JSONArray();
                        for (int i = 0; i < resuleSkuArray.size(); i++) {
                            JSONObject sku = new JSONObject();
                            JSONObject resultSku = resuleSkuArray.getJSONObject(i);

                            sku.put("sku_stock",resultSku.get("sku_stock"));
                            sku.put("sku_price",resultSku.get("sku_price"));
                            sku.put("sku_code",resultSku.get("sku_code"));
                            sku.put("ipm_sku_stock",resultSku.get("ipm_sku_stock"));
                            sku.put("id",resultSku.get("id"));
                            sku.put("currency_code",resultSku.get("currency_code"));
                            sku.put("sku_discount_price",resultSku.get("sku_discount_price"));
                            if(resultSku.containsKey("aeop_s_k_u_property_list")) {
                                JSONObject skuPropertyList = resultSku.getJSONObject("aeop_s_k_u_property_list");
                                sku.put("aeop_s_k_u_property", skuPropertyList.getJSONArray("aeop_sku_property"));
                            }
                            skus.add(sku);
                        }
                        updateObj.put("aeop_ae_product_s_k_us", skus);
                    }
                    updateObj.put("detail", obj.get("detail"));
                    updateObj.put("package_type", obj.get("package_type"));
                    updateObj.put("freight_template_id", obj.get("freight_template_id"));
                    updateObj.put("add_unit", obj.get("add_unit"));
                    updateObj.put("subject", obj.get("subject"));
                    updateObj.put("product_more_keywords1", obj.get("product_more_keywords1"));
                    updateObj.put("reduce_strategy", obj.get("reduce_strategy"));
                    updateObj.put("product_more_keywords2", obj.get("product_more_keywords2"));
                    updateObj.put("product_unit", obj.get("product_unit"));
                    updateObj.put("ws_offline_date", obj.get("ws_offline_date"));
                    updateObj.put("base_unit", obj.get("base_unit"));
                    updateObj.put("sizechart_id", obj.get("sizechart_id"));
                    updateObj.put("package_length", obj.get("package_length"));
                    updateObj.put("ws_display", obj.get("ws_display"));
                    updateObj.put("mobile_detail", obj.get("mobile_detail"));
                    updateObj.put("package_height", obj.get("package_height"));
                    updateObj.put("is_image_dynamic", obj.get("is_image_dynamic"));
                    updateObj.put("src", obj.get("src"));
                    updateObj.put("coupon_start_date", obj.get("coupon_start_date"));
                    updateObj.put("package_width", obj.get("package_width"));
                    updateObj.put("is_pack_sell", obj.get("is_pack_sell"));
                    updateObj.put("currency_code", obj.get("currency_code"));
                    updateObj.put("owner_member_seq", obj.get("owner_member_seq"));
                    Object category_id = obj.get("category_id");

                    updateObj.put("category_id", category_id);
                    updateObj.put("keyword", obj.get("keyword"));
                    updateObj.put("image_u_r_ls", obj.get("image_u_r_ls"));
                    updateObj.put("coupon_end_date", obj.get("coupon_end_date"));

                    if(obj.containsKey("aeop_a_e_multimedia")){
                        JSONObject aeop_a_e_multimediaJson = obj.getJSONObject("aeop_a_e_multimedia");
                        if(aeop_a_e_multimediaJson.containsKey("aeop_a_e_videos")){
                            JSONObject aeop_a_e_videosJson = aeop_a_e_multimediaJson.getJSONObject("aeop_a_e_videos");
                            if(aeop_a_e_videosJson.containsKey("aeop_ae_video")){
                                JSONArray aeop_ae_video = aeop_a_e_videosJson.getJSONArray("aeop_ae_video");
                                if(aeop_ae_video != null){
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("aeop_a_e_videos", aeop_ae_video);
                                    updateObj.put("aeop_a_e_multimedia", jsonObject);
                                }
                            }
                        }

                    }

                    updateObj.put("owner_member_id", obj.get("owner_member_id"));
                    updateObj.put("product_status_type", obj.get("product_status_type"));
                    if(obj.containsKey("aeop_ae_product_propertys")) {
                        JSONObject propertys = obj.getJSONObject("aeop_ae_product_propertys");

                        //TODO 如果类目有产地，需要加上中国
                        JSONArray initJSONArray = propertys.getJSONArray("aeop_ae_product_property");

                        String addOrigin = AliexpressBrandUtils
                                .addOrigin(initJSONArray.toJSONString(), saleAccountByAccountNumber,
                                        category_id.toString());

                        JSONArray originJSONArray = JSON.parseArray(addOrigin);
                        updateObj.put("aeop_ae_product_propertys",
                                originJSONArray);
                    }
                    updateObj.put("add_weight", obj.get("add_weight"));
                    updateObj.put("gross_weight", obj.get("gross_weight"));
                    updateObj.put("product_id", obj.get("product_id"));
                    updateObj.put("group_id", obj.get("group_id"));
                    updateObj.put("delivery_time", obj.get("delivery_time"));
                    updateObj.put("ws_valid_num", obj.get("ws_valid_num"));
                    updateObj.put("bulk_discount", obj.get("bulk_discount"));
                    updateObj.put("promise_template_id", obj.get("promise_template_id"));
                    updateObj.put("product_price", obj.get("product_price"));

                    updateObj.put("aeop_national_quote_configuration", obj.get("aeop_national_quote_configuration"));

                    return updateObj;
                }
            }
        }
        return null;
    }

    public static void syncProductForUpdate(List<AliexpressEditProductBean> excelDataList, Map<String, JSONObject> editProductEntityMap,
            Map<String, UpdatePriceEntity> editProductReturnMap) {
        if(CollectionUtils.isNotEmpty(excelDataList)) {
            for (AliexpressEditProductBean item : excelDataList) {
                String productId = item.getProductId();
                JSONObject quoteConfigJson = new JSONObject();
                try {
                    if(!editProductEntityMap.containsKey(productId)) {
                        String accountNum = item.getAccountNum();

                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNum);
                        JSONObject productEntity = OfferQueryProductOpenCall.transResultToOfferUpdate(saleAccountByAccountNumber, Long.valueOf(productId));

                        UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                        updatePriceEntity.setSeller(item.getAccountNum());
                        updatePriceEntity.setProductId(item.getProductId());
                        updatePriceEntity.setSkuCode(item.getSkuCode());
                        updatePriceEntity.setTidbUniqueId(item.getUniqueId());
                        updatePriceEntity.setBatchId(item.getBatchId());

                        if (null != productEntity) {
                            productEntity.put("aeop_national_quote_configuration", AliexpressStatePriceUtils.transQuote(item, null));
                            if(item.getTemplateId() != null){
                                productEntity.put("freight_template_id", item.getTemplateId());
                            }
                            editProductEntityMap.put(productId, productEntity);
                        } else {
                            updatePriceEntity.setErrorTip("修改失败-同步产品数据错误");
                        }
                        editProductReturnMap.put(productId, updatePriceEntity);
                    }else {
                        JSONObject productEntity = editProductEntityMap.get(productId);
                        quoteConfigJson = productEntity.getJSONObject("aeop_national_quote_configuration");
                        productEntity.put("aeop_national_quote_configuration", AliexpressStatePriceUtils.transQuote(item, quoteConfigJson));
                    }

                }catch(Exception e) {
                    log.error("sql报错---" + item.getProductId() + "，" + item.getAccountNum() + e.getMessage());
                }
            }
        }
    }


    /**
     * 解析32国价格数据 价格核对用
     * @param
     * @param
     * @return
     */
    public static Map<String, Map<String,Double>> getProduct28MapForEs(AliexpressEsExtend extend, Map<String, Map<String, Double>> product28Map){
        Map<String, String> skuIdCodeMap = new HashMap<>();
        if(StringUtils.isNotBlank(extend.getAeopAeProductSkusJson())) {
            try {
                JSONArray skuArray = JSON.parseArray(extend.getAeopAeProductSkusJson());
                for (int i = 0; i < skuArray.size(); i++) {
                    JSONObject sku = skuArray.getJSONObject(i);
                    String id = sku.getString("id");
                    String skuCode = sku.getString("sku_code");
                    skuIdCodeMap.put(id, skuCode);
                }
            }catch(Exception e) {
                throw new BusinessException("产品信息有误，请同步后重试");
            }
        }else {
            throw new BusinessException("产品信息有误，请同步后重试");
        }
        if (StringUtils.isNotBlank(extend.getAeopNationalQuoteConfiguration())) {
            try {
                JSONObject statePriceConfigObj = JSON.parseObject(extend.getAeopNationalQuoteConfiguration());
                if(statePriceConfigObj != null) {
                    JSONArray priceConfigArray = statePriceConfigObj.getJSONArray("configuration_data");
                    String configType = statePriceConfigObj.getString("configuration_type");

                    //类型
                    extend.setPriceType(configType);

                    Set<String> shiptoCountrySet = new HashSet<>();

                    for (int i = 0; i < priceConfigArray.size(); i++) {
                        JSONObject resultSku = priceConfigArray.getJSONObject(i);
                        String shiptoCountry = resultSku.getString("shiptoCountry");
                        shiptoCountrySet.add(shiptoCountry);

                        Map<String, String> priceMap = resultSku.getObject("absoluteQuoteMap", Map.class);

                        for(Entry<String, String> item : priceMap.entrySet()) {

                            String skuId = StringUtils.isBlank(item.getKey()) ? "<none>" : item.getKey();

                            String value = String.valueOf(item.getValue());

                            //skuCode
                            String skuCode = skuIdCodeMap.get(skuId);

                            String key = extend.getProductId() + "-" + skuCode;

                            //存放当前 skuCode 对应的32国价格
                            Map<String, Double> stringDoubleMap = product28Map.get(key);
                            if(stringDoubleMap == null){
                                stringDoubleMap = new HashMap<>();
                                product28Map.put(key, stringDoubleMap);
                            }

                            stringDoubleMap.put(shiptoCountry, Double.parseDouble(value));
                        }
                    }
                    extend.setShiptoCountryList(new ArrayList<>(shiptoCountrySet));
                }
            }catch(Exception e) {
                throw new BusinessException("32国价格信息解析异常" + e.getMessage());
            }
        }
        return product28Map;
    }



    /**
     * 解析32国价格数据 清空区域调价用
     * @param extend
     * @param product28Map
     * @param countryCodeList
     * @return
     */
    public static void getProduct28MapForEs(AliexpressEsExtend extend, Map<String, Map<String, Double>> product28Map, List<String> countryCodeList) {
        Long productId = extend.getProductId();
        //判断是否属于半托管产品，如果是就需要去除半托管产品的区域国家
        AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
        halfTgItemExample.createCriteria().andProductIdEqualTo(productId).andProductStatusEqualTo("onSelling");
        List<AliexpressHalfTgItem> aliexpressHalfTgItems = aliexpressHalfTgItemService.selectByExample(halfTgItemExample);
        if(CollectionUtils.isNotEmpty(aliexpressHalfTgItems)){
            //["ES","FR","BR","US","IL","MX","CL","UA","PL","DE","UK","NL","IT","AU","MY","TH","PT","BE","CH","CZ","NZ","LT","LV","SK","NO","HU","BG","EE","RO","PK","HR","NG","IE","AT","GR","SE","FI","DK","SI","MT","LK","LU","PE","SG","CA","CY","ET","KR"]
            String joinedCountryList = aliexpressHalfTgItems.get(0).getJoinedCountryList();
            List<String> strings = JSONArray.parseArray(joinedCountryList, String.class);
            countryCodeList.removeAll(strings);
            if(CollectionUtils.isEmpty(countryCodeList)){
                countryCodeList = new ArrayList<>();
            }
        }

        Map<String, String> skuIdCodeMap = new HashMap<>();
        if (StringUtils.isNotBlank(extend.getAeopAeProductSkusJson())) {
            try {
                JSONArray skuArray = JSON.parseArray(extend.getAeopAeProductSkusJson());
                for (int i = 0; i < skuArray.size(); i++) {
                    JSONObject sku = skuArray.getJSONObject(i);
                    String id = sku.getString("id");
                    String skuCode = sku.getString("sku_code");
                    skuIdCodeMap.put(id, skuCode);
                }
            } catch (Exception e) {
                throw new BusinessException("产品信息有误，请同步后重试");
            }
        } else {
            throw new BusinessException("产品信息有误，请同步后重试");
        }
        if (StringUtils.isNotBlank(extend.getAeopNationalQuoteConfiguration())) {
            try {
                JSONObject statePriceConfigObj = JSON.parseObject(extend.getAeopNationalQuoteConfiguration());
                if(statePriceConfigObj != null) {
                    JSONArray priceConfigArray = statePriceConfigObj.getJSONArray("configuration_data");
                    String configType = statePriceConfigObj.getString("configuration_type");

                    // 类型
                    extend.setPriceType(configType);
                    for (int i = 0; i < priceConfigArray.size(); i++) {
                        JSONObject resultSku = priceConfigArray.getJSONObject(i);
                        String shiptoCountry = resultSku.getString("shiptoCountry");

                        // 如果是选中的国家，则将价格清空
                        if (countryCodeList.contains(shiptoCountry)) {
                            continue;
                        }

                        Map<String, String> priceMap = resultSku.getObject("absoluteQuoteMap", Map.class);

                        for (Entry<String, String> item : priceMap.entrySet()) {
                            String skuId = StringUtils.isBlank(item.getKey()) ? "<none>" : item.getKey();
                            String value = String.valueOf(item.getValue());

                            // skuCode
                            String skuCode = skuIdCodeMap.get(skuId);

                            String key = extend.getProductId() + "-" + skuCode;

                            // 存放当前 skuCode 对应的32国价格
                            Map<String, Double> stringDoubleMap = product28Map.get(key);
                            if (stringDoubleMap == null) {
                                stringDoubleMap = new HashMap<>();
                                product28Map.put(key, stringDoubleMap);
                            }

                            stringDoubleMap.put(shiptoCountry, Double.parseDouble(value));
                        }
                    }
                }
            } catch(Exception e) {
                throw new BusinessException("32国价格信息解析异常" + e.getMessage());
            }
        }
    }

    /**
     * 解析32国价格数据 es数据
     * @param
     * @param
     * @return
     */
    public static List<AliexpressProductStatePriceEntity> getStateEntityListForEs(EsAliexpressProductListing esProduct){
        Long productId = esProduct.getProductId();
        String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();

        if(StringUtils.isEmpty(aliexpressAccountNumber) || productId == null){
            log.warn("32国下载价格条件为空");
            return new ArrayList<>();
        }

        AliexpressEsExtendService aliexpressEsExtendService = SpringUtils.getBean(AliexpressEsExtendService.class);
        AliexpressEsExtend extend = aliexpressEsExtendService
                .selectByAccountandProductId(aliexpressAccountNumber, productId);
        if(extend == null){
            throw new BusinessException("产品信息有误，请同步后重试");
        }

        Map<String, String> skuIdCodeMap = new HashMap<>();
        Map<String, AliexpressProductStatePriceEntity> skuIdConfigMap = new HashMap<>();
        if(StringUtils.isNotBlank(extend.getAeopAeProductSkusJson())) {
            try {
                JSONArray skuArray = JSON.parseArray(extend.getAeopAeProductSkusJson());
                for (int i = 0; i < skuArray.size(); i++) {
                    JSONObject sku = skuArray.getJSONObject(i);
                    String id = sku.getString("id");
                    String skuCode = sku.getString("sku_code");

                    AliexpressProductStatePriceEntity entity = new AliexpressProductStatePriceEntity();
                    entity.setAccountNumber(extend.getAliexpressAccountNumber());
                    entity.setProductId(extend.getProductId());
                    entity.setSkuId(id);
                    entity.setSkuCode(skuCode);
                    entity.setPriceType("无");
                    skuIdConfigMap.put(id, entity);
                    skuIdCodeMap.put(id, skuCode);
                }
            }catch(Exception e) {
                throw new BusinessException("产品信息有误，请同步后重试");
            }
        }else {
            throw new BusinessException("产品信息有误，请同步后重试");
        }
        if (StringUtils.isNotBlank(extend.getAeopNationalQuoteConfiguration())) {
            try {
                JSONObject statePriceConfigObj = JSON.parseObject(extend.getAeopNationalQuoteConfiguration());
                if(statePriceConfigObj != null) {
                    JSONArray priceConfigArray = statePriceConfigObj.getJSONArray("configuration_data");
                    String configType = statePriceConfigObj.getString("configuration_type");
                    for (int i = 0; i < priceConfigArray.size(); i++) {
                        JSONObject resultSku = priceConfigArray.getJSONObject(i);
                        String shiptoCountry = resultSku.getString("shiptoCountry");
                        Map<String, String> priceMap = resultSku.getObject("absoluteQuoteMap", Map.class);
                        for(Entry<String, String> item : priceMap.entrySet()) {
                            String skuId = StringUtils.isBlank(item.getKey()) ? "<none>" : item.getKey();
                            String value = String.valueOf(item.getValue());
                            AliexpressProductStatePriceEntity entity = skuIdConfigMap.get(skuId);
                            if(entity != null) {
                                entity.setValue(shiptoCountry, value);
                                entity.setPriceType(configType);
                            }
                            skuIdConfigMap.put(skuId, entity);
                        }
                    }
                }
            }catch(Exception e) {
                log.error(e.getMessage(), e);
                throw new BusinessException("32国价格信息有误，请同步后重试");
            }
        }
        List<AliexpressProductStatePriceEntity> resultList = new ArrayList<>();
        for(Entry<String, AliexpressProductStatePriceEntity> item : skuIdConfigMap.entrySet()) {
            resultList.add(item.getValue());
        }
        return resultList;
    }

    /**
     * 读取excel有效数据
     * @param headerState
     * @param file
     * @return
     * @throws IOException
     */
    public static Map<String, List<AliexpressEditProductBean>> getDataFromExcelForEs(String[] headerState, MultipartFile file, String priceType) throws IOException {
        Map<String, List<AliexpressEditProductBean>> returnMap = new HashMap<>();

        POIUtils.readExcelSheet1(headerState, file, row -> {
            try {
                if(row == null) {
                    return null;
                }
                if(ExcelUtils.isNotBlankCell(row.getCell(2)) && ExcelUtils.isNotBlankCell(row.getCell(3))) {
                    String productId = ExcelUtils.getCellValue(row.getCell(3)).trim();
                    List<AliexpressEditProductBean> excelDataList = new ArrayList<AliexpressEditProductBean>();
                    if(returnMap.containsKey(productId)) {
                        excelDataList = returnMap.get(productId);
                    }
                    AliexpressEditProductBean item = new AliexpressEditProductBean();
                    item.setAccountNum(ExcelUtils.getCellValue(row.getCell(2)).trim());
                    item.setProductId(productId);
                    String skuCode = ExcelUtils.getCellValue(row.getCell(5)).trim();
                    item.setSkuCode(skuCode);

                    String skuId = ExcelUtils.getCellValue(row.getCell(23)).trim();
                    item.setSkuId(skuId);
                    // 就取excel里面的sku_id
//                    if(StringUtils.isBlank(skuCode)){
//                        item.setSkuId(skuId);
//                    }else{
//                        EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
//
//                        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
//                        esRequest.setQueryFields(new String[]{"skuId"});
//                        esRequest.setProductId(Long.valueOf(productId));
//                        esRequest.setArticleNumber(skuCode);
//                        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
//                                .getEsAliexpressProductListing(esRequest);
//                        if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
//                            item.setSkuId(esAliexpressProductListing.get(0).getSkuId());
//                        }else{
//                            item.setSkuId(skuId);
//                        }
//                    }
                    item.setPriceType(priceType);
                    Map<String, Double> countryPriceMap = new HashMap<String, Double>();
                    for(int i = 0; i < countryList.length; i++) {
                        String country = countryList[i];
                        Cell cell = row.getCell(25+i);
                        if(ExcelUtils.isNotBlankCell(cell)) {

                            try {
                                Double price = ExcelUtils.getDoubleCellValue(cell);
                                countryPriceMap.put(country, price);
                            }
                            catch (Exception e) {
                                String trim =  ExcelUtils.getCellValue(cell);
                                if(StringUtils.isNotBlank(trim)){
                                    Double price =  Double.parseDouble(trim);
                                    countryPriceMap.put(country, price);
                                }
                            }
                        }
                    }
                    item.setCountryPriceMap(countryPriceMap);
                    excelDataList.add(item);
                    returnMap.put(productId, excelDataList);
                    return row;
                }
            }catch(Exception e) {
                log.error("Excel数据错误：rowNum:" + (row.getRowNum()+1));
            }
            return null;
        }, false);
        return returnMap;
    }


    /**
     * 刊登成功上传32国价格
     *
     * @param saleAccountByAccountNumber
     * @param productId
     * @param creator
     * @param areaDiscountRate 店铺配置的折扣率，如果有值就不能取 63国配置的折扣率
     */
    public static void calePriceAndUpload(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Long productId, String creator, Double areaDiscountRate) {
        String accountNumber = saleAccountByAccountNumber.getAccountNumber();
        List<AliexpressProductForAreaPrice> aliexpressProductForAreaPrices = aliexpressProductForAreaPriceService.selectByAccountAndProductId(accountNumber, productId);
        if(CollectionUtils.isEmpty(aliexpressProductForAreaPrices)){
            log.error("产品id:{},在线列表es查不到数据", productId);
            return;
        }
        List<EsAliexpressProductListing> esAliexpressProductListing = EsTranProductUtils.tranEsAliexpressProductListing(aliexpressProductForAreaPrices);

//        EsAliexpressProductListingRequest productListingRequest = new EsAliexpressProductListingRequest();
//        productListingRequest.setAliexpressAccountNumber(accountNumber);
//        productListingRequest.setProductId(productId);
//        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(productListingRequest);
//        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
//            log.error("产品id:{},在线列表es查不到数据", productId);
//            return;
//        }

        //---------------改版前的逻辑部分-------------------------
//        // 根据最大重量和标签匹配配置
//        AliexpressConfigInfo matchConfigInfo = getConfigByMaxWeightAndTagCode(weightTagCode, aliexpressConfig.getInfoList());
//        if (null == matchConfigInfo) {
//            log.error(String.format("店铺[%s] 产品重量[%s] 标签[%s] 获取不到配置", accountNumber, weightTagCode.getMaxWeight(), weightTagCode.getTagCode()));
//            return;
//        }
//
//        // 根据配置id查询算价规则配置
//        List<AliexpressConfigPriceTrial> aliexpressConfigPriceTrials = aliexpressConfigPriceTrialService.selectByCalcConfigId(matchConfigInfo.getId());
//        if (CollectionUtils.isEmpty(aliexpressConfigPriceTrials)) {
//            log.error(String.format("店铺[%s] 配置id[%s] 没有算价规则配置", accountNumber, matchConfigInfo.getId()));
//            return;
//        }
//
//        // 匹配区间算价物流模板以及产品分组id
//        MatchShippingBean matchShippingBean = matchCalcShipping(weightTagCode.getSku(), aliexpressConfigPriceTrials);
//        //需要自动上传32国价格
//        Integer shippingId = matchShippingBean.getShippingId();
//        AliInternationalCaleConfig aliInternationalCaleConfig = aliInternationalCaleConfigService
//                .selectByPrimaryKey(shippingId);
//        if (null == aliInternationalCaleConfig) {
//            throw new BusinessException("物流模板id：" + shippingId + "查询不到配置信息");
//        }
//        String tempName = aliInternationalCaleConfig.getTempName();
//        String createBy = aliInternationalCaleConfig.getCreateBy();
//        if (StringUtils.isBlank(tempName) || StringUtils.isBlank(createBy)) {
//            throw new BusinessException("物流名称为空或者创建人为空请检查！");
//        }

        //-----------------------------------------------------

        //---------------改版后的逻辑部分-------------------------
        // 匹配运费模板id
        Long freightTemplateId = esAliexpressProductListing.get(0).getFreightTemplateId();
        if (freightTemplateId == null) {
            log.error("产品id:{},运费模板为空", productId);
            return;
        }

        String key = RedisConstant.SMT_FREIGHT_TEMPLATE + accountNumber + ":" + freightTemplateId;
        String templateName = PublishRedisClusterUtils.get(
                key);

        if(StringUtils.isBlank(templateName)){
            FreightTemplateOpenCall call = new FreightTemplateOpenCall();
            call.getFreightTemplateList(saleAccountByAccountNumber);
            templateName = PublishRedisClusterUtils.get(
                    key);
        }
        if (StringUtils.isBlank(templateName)) {
            String errorMsg = String.format("运费模板:%s,找不到对应数据", freightTemplateId);
            log.error(errorMsg);
            throw new BusinessException(errorMsg);
        }
        if (StringUtils.isBlank(creator)) {
            throw new BusinessException("创建人为空请检查！");
        }

        Map<String, AliexpressEsExtend> esExtendMap = new HashMap<>();
        ResponseJson responseJson;
        try {
            responseJson = aliexpressEsExtendService.product28Calc(esAliexpressProductListing, esExtendMap, templateName,
                    creator, null, null, true);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (!responseJson.isSuccess()) {
            throw new RuntimeException("产品id:" + productId + " 调价异常:" + responseJson.getMessage());
        }

        //es主键 对应的区域国家调价集合
        Map<String, List<BatchPriceCalculatorResponse>> map = (Map<String, List<BatchPriceCalculatorResponse>>)responseJson.getBody().get("key");
        for (Map.Entry<String, List<BatchPriceCalculatorResponse>> stringListEntry : map.entrySet()) {
            List<BatchPriceCalculatorResponse> value = stringListEntry.getValue();
            for (BatchPriceCalculatorResponse calcRsp : value) {
                if(!calcRsp.getIsSuccess()){
                    throw new RuntimeException("产品id:" + esAliexpressProductListing.get(0).getProductId() + " 调价异常:" + calcRsp.getErrorMsg());
                }
            }
        }

//        AliInternationalCaleConfigExample example = new AliInternationalCaleConfigExample();
//        example.createCriteria().andTempNameEqualTo(templateName).andCreateByEqualTo(creator);
//        List<AliInternationalCaleConfig> aliInternationalCaleConfigs = aliInternationalCaleConfigService
//                .selectByExample(example);

        //---------------改版后的逻辑部分-------------------------
        //获取物流配置,优先级:本人创建的 > 公用的
        List<AliInternationalCaleConfig> aliInternationalCaleConfigs = AliexpressCalcPriceUtil.getPriorityCaleConfigs(templateName, creator);
        //-----------------------------------------------------
        if (CollectionUtils.isEmpty(aliInternationalCaleConfigs)) {
            throw new RuntimeException("产品id:" + productId + " 配置名称 " + templateName + "算价物流配置不存在！");
        }

        //国家对应的店铺折扣
        Map<String, Double> codeDiscountRateMap = new HashMap<>();
        for (AliInternationalCaleConfig caleConfig : aliInternationalCaleConfigs) {
            String countryCode = caleConfig.getCountryCode();
            Double discountRate = caleConfig.getDiscountRate();
            if(areaDiscountRate != null){
                log.info("产品id:{} 国家:{} 算价配置折扣率:{} ,匹配店铺配置折扣率 : {}", productId, countryCode, discountRate, areaDiscountRate);
                codeDiscountRateMap.put(countryCode, areaDiscountRate);
            }else{
                codeDiscountRateMap.put(countryCode, discountRate);
            }
        }

        //组装数据
        Map<String, List<AliexpressEditProductBean>> returnMap = new HashMap<>();
        List<AliexpressEditProductBean> excelDataList = new ArrayList<>();
        for (EsAliexpressProductListing productListing : esAliexpressProductListing) {
            String id = productListing.getId();
            List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = map.get(id);

            //先用毛利算出来价格X，折扣价=X/(1-折扣)
            //单个产品国家对于的价格
            Map<String, Double> countryCodeMap = new HashMap<>();
            for (BatchPriceCalculatorResponse batchPriceCalculatorRespons : batchPriceCalculatorResponses) {
                String countryCode = batchPriceCalculatorRespons.getCountryCode();
                Double foreignPrice = batchPriceCalculatorRespons.getForeignPrice();
                //全部按照直接调价上传
                Double discountRate = codeDiscountRateMap.get(countryCode);
                if(discountRate == null){
                    discountRate = 0.0d;
                }
                foreignPrice = NumberUtils.round(foreignPrice /(1- discountRate) , 2);
                //有折扣 类型转换
                countryCodeMap.put(countryCode, foreignPrice);
            }

            AliexpressEditProductBean item = new AliexpressEditProductBean();
            item.setAccountNum(accountNumber);
            item.setProductId(productId.toString());
            item.setSkuCode(productListing.getArticleNumber());//存的是货号
            item.setSkuId(productListing.getSkuId());
            item.setPriceType("absolute");
            //调价的国家
            item.setCountryPriceMap(countryCodeMap);
            excelDataList.add(item);
        }
        returnMap.put(productId.toString(), excelDataList);
        aliexpressEsExtendService.updateProductCountryPriceNew(returnMap, PreCheckUtils.admin_auto, null,false, isMySql);
    }

    private static Pair<String, Long> matchCalcFreightTemplate(WeightTagCode weightTagCode, List<AliexpressConfigInfo> matchConfigInfos, SaleAccountAndBusinessResponse saleAccountByAccountNumber) {
        if (StringUtils.isBlank(weightTagCode.getSku()) || CollectionUtils.isEmpty(matchConfigInfos)) {
            throw new IllegalStateException("参数不全，无法匹配算价规则");
        }

        StringBuilder msg = new StringBuilder("不进行区间调价：");
        //按照【试算物流-毛利率-国家】分组
        Map<String, List<AliexpressConfigInfo>> configInfosGroup = matchConfigInfos.stream()
                .collect(Collectors.groupingBy(info -> info.getShippingMethod() + "-" + info.getGrossProfit() + "-" + info.getCountryCode()));
        for (Entry<String, List<AliexpressConfigInfo>> entry : configInfosGroup.entrySet()) {
            List<AliexpressConfigInfo> list = entry.getValue();
            Integer id = list.get(0).getId();
            String shippingMethod = list.get(0).getShippingMethod();
            Double grossProfit = list.get(0).getGrossProfit();
            String countryCode = list.get(0).getCountryCode();
            //算价
            BigDecimal price = calcPrice(weightTagCode.getSku(), shippingMethod, grossProfit, countryCode);
            msg.append(String.format("[配置主键:%s, 试算物流:%s, 毛利率:%s, 国家:%s]", id, shippingMethod, grossProfit, countryCode));
            AliexpressConfigInfo aliexpressConfigInfo = list.stream().filter(info -> {
                Double fromPrice = info.getFromPrice();
                if (null == fromPrice) {
                    fromPrice = 0d;
                }
                Double toPrice = info.getToPrice();
                if (null == toPrice) {
                    toPrice = Double.MAX_VALUE;
                }

                return BigDecimal.valueOf(fromPrice).compareTo(price) < 0 && price.compareTo(BigDecimal.valueOf(toPrice)) <= 0;
            }).findFirst().orElse(null);

            if (null == aliexpressConfigInfo || null == aliexpressConfigInfo.getFreightTemplateId()) {
                msg.append(String.format(",试算价格为:%s，不满足区间或运费模板为空", price));
                continue;
            }

            FreightTemplateOpenCall freightCall = new FreightTemplateOpenCall();
            List<AliexpressFreightTemplate> freightTemplates = freightCall.getFreightTemplateList(saleAccountByAccountNumber);
            if (CollectionUtils.isNotEmpty(freightTemplates)) {
                AliexpressFreightTemplate freightTemplate = freightTemplates.stream()
                        .filter(template -> aliexpressConfigInfo.getFreightTemplateId().equals(template.getTemplateId())).findFirst().orElse(null);
                if (null != freightTemplate) {
                    return Pair.of(freightTemplate.getTemplateName(), aliexpressConfigInfo.getGroupId());
                }
            }
            msg.append(String.format(",运费模板:%s,找不到对应数据", aliexpressConfigInfo.getFreightTemplateId()));
        }
        throw new RuntimeException(String.valueOf(msg));
    }

    private static MatchShippingBean matchCalcShipping(String sku, List<AliexpressConfigPriceTrial> aliexpressConfigPriceTrials) {
        if (StringUtils.isBlank(sku) || CollectionUtils.isEmpty(aliexpressConfigPriceTrials)) {
            throw new IllegalStateException("参数不全，无法匹配算价规则");
        }

        // 获取第一层级进行计算
        List<AliexpressConfigPriceTrial> headPriceTrials = aliexpressConfigPriceTrials.stream().filter(o -> null == o.getParentId()).collect(Collectors.toList());

        // 算价
        BigDecimal price = calcPrice(sku, headPriceTrials.get(0));
        for (AliexpressConfigPriceTrial headPriceTrial : headPriceTrials) {
            boolean meetPriceRange = judgeIsMeetPriceRange(price, headPriceTrial);
            if (!meetPriceRange) {
                continue;
            }

            // 如果符合价格区间，判断当前层级是否存在物流模板，如果存在，直接跳出
            if (null != headPriceTrial.getShippingTemplateId()) {
                MatchShippingBean matchShippingBean = new MatchShippingBean();
                matchShippingBean.setShippingId(headPriceTrial.getShippingTemplateId().intValue());
                matchShippingBean.setGroupId(headPriceTrial.getGroupId());
                return matchShippingBean;
            }

            // 如果不存在物流模板 则计算下一层级
            return matchChildren(sku, headPriceTrial.getId(), aliexpressConfigPriceTrials);
        }

        throw new RuntimeException("试算价格为：" + price + "，不满足区间，不进行区间调价");
    }

    private static MatchShippingBean matchChildren(String sku, Integer parentId, List<AliexpressConfigPriceTrial> aliexpressConfigPriceTrials) {
        List<AliexpressConfigPriceTrial> childrenList = aliexpressConfigPriceTrials.stream().filter(o -> parentId.equals(o.getParentId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(childrenList)) {
            throw new RuntimeException("算价规则树结构配置异常");
        }

        // 算价
        BigDecimal price = calcPrice(sku, childrenList.get(0));
        for (AliexpressConfigPriceTrial priceTrial : childrenList) {
            boolean meetPriceRange = judgeIsMeetPriceRange(price, priceTrial);
            if (!meetPriceRange) {
                continue;
            }

            // 如果符合价格区间，判断当前层级是否存在物流模板，如果存在，直接跳出
            if (null != priceTrial.getShippingTemplateId()) {
                MatchShippingBean matchShippingBean = new MatchShippingBean();
                matchShippingBean.setShippingId(priceTrial.getShippingTemplateId().intValue());
                matchShippingBean.setGroupId(priceTrial.getGroupId());
                return matchShippingBean;
            }

            // 如果不存在物流模板 则计算下一层级
            return matchChildren(sku, priceTrial.getId(), aliexpressConfigPriceTrials);
        }

        throw new RuntimeException("试算价格为：" + price + "，不满足区间，不进行区间调价");
    }

    private static BigDecimal calcPrice(String sku, AliexpressConfigPriceTrial priceTrial) {
        String currencyCode = "USD";
        Double grossProfitRate = priceTrial.getGrossProfit();
        String countryCode = priceTrial.getCountry();
        String shippingMethodCode = priceTrial.getShippingMethod();
        ResponseJson calcPriceRsp = AliexpressCalcPriceUtil
                .publishCalc(Lists.newArrayList(sku), shippingMethodCode, grossProfitRate, countryCode, currencyCode);
        if (!calcPriceRsp.isSuccess()) {
            throw new RuntimeException(calcPriceRsp.getMessage());
        }
        Map<String, BatchPriceCalculatorResponse> resultMap = (Map<String, BatchPriceCalculatorResponse>) calcPriceRsp.getBody().get(AliexpressCalcPriceUtil.key);
        BatchPriceCalculatorResponse calcRsp = resultMap.get(sku);
        if (calcRsp == null) {
            throw new NoSuchElementException(sku + "子sku没有算价信息");
        }
        if (!calcRsp.getIsSuccess()) {
            throw new RuntimeException(sku + "子sku算价异常" + calcRsp.getErrorMsg());
        }

        return BigDecimal.valueOf(calcRsp.getForeignPrice()).setScale(2, RoundingMode.HALF_UP);
    }

    private static BigDecimal calcPrice(String sku, String shippingMethodCode, Double grossProfitRate, String countryCode) {
        String currencyCode = "USD";
        ResponseJson calcPriceRsp = AliexpressCalcPriceUtil
                .publishCalc(Lists.newArrayList(sku), shippingMethodCode, grossProfitRate, countryCode, currencyCode);
        if (!calcPriceRsp.isSuccess()) {
            throw new RuntimeException(calcPriceRsp.getMessage());
        }
        Map<String, BatchPriceCalculatorResponse> resultMap = (Map<String, BatchPriceCalculatorResponse>) calcPriceRsp.getBody().get(AliexpressCalcPriceUtil.key);
        BatchPriceCalculatorResponse calcRsp = resultMap.get(sku);
        if (calcRsp == null) {
            throw new NoSuchElementException(sku + "子sku没有算价信息");
        }
        if (!calcRsp.getIsSuccess()) {
            throw new RuntimeException(sku + "子sku算价异常" + calcRsp.getErrorMsg());
        }

        return BigDecimal.valueOf(calcRsp.getForeignPrice()).setScale(2, RoundingMode.HALF_UP);
    }

    private static boolean judgeIsMeetPriceRange(BigDecimal price, AliexpressConfigPriceTrial priceTrial) {
        Double fromPrice = priceTrial.getFromPrice();
        if (null == fromPrice) {
            fromPrice = 0d;
        }
        Double toPrice = priceTrial.getToPrice();
        if (null == toPrice) {
            toPrice = Double.MAX_VALUE;
        }

        return BigDecimal.valueOf(fromPrice).compareTo(price) < 0 && price.compareTo(BigDecimal.valueOf(toPrice)) <= 0;
    }

    /**
     * 根据重量和标签匹配算价配置
     */
    public static AliexpressConfigInfo getConfigByMaxWeightAndTagCode(WeightTagCode weightTagCode, List<AliexpressConfigInfo> infoList) {
        if (weightTagCode == null || CollectionUtils.isEmpty(infoList)) {
            return null;
        }
        Double weight = weightTagCode.getMaxWeight();
        String skuTagCode = weightTagCode.getTagCode();
        String skuSpecialTags = weightTagCode.getSpecialTags();

        // 优先根据重量和特殊标签匹配
        AliexpressConfigInfo matchConfigInfo = infoList.stream().filter(info -> {
            Double fromWeight = info.getFromWeight();
            Double toWeight = info.getToWeight() == null ? Double.MAX_VALUE : info.getToWeight();
            String specialTags = info.getSpecialTagCode();
            if (!(weight.compareTo(fromWeight) < 0) && weight.compareTo(toWeight) < 0) {
                if (StringUtils.isBlank(specialTags) || StringUtils.isBlank(skuSpecialTags)) {
                    return false;
                }

                List<String> specialTagList = CommonUtils.splitList(specialTags, ",");
                List<String> skusSpecialTagList = CommonUtils.splitList(skuSpecialTags, ",");
                // 取交集
                specialTagList.retainAll(skusSpecialTagList);

                return CollectionUtils.isNotEmpty(specialTagList);
            }
            return false;
        }).findFirst().orElse(null);
        if (matchConfigInfo != null) {
            return matchConfigInfo;
        }

        // 再根据重量和标签匹配
        for (AliexpressConfigInfo aliexpressConfigInfo : infoList) {
            Double fromWeight = aliexpressConfigInfo.getFromWeight();
            Double toWeight = aliexpressConfigInfo.getToWeight() == null ? Double.MAX_VALUE : aliexpressConfigInfo.getToWeight();

            // 配置的产品标签
            String tagCodes = aliexpressConfigInfo.getTagCodes();

            // 重量配置第一匹配
            if (!(weight.compareTo(fromWeight) < 0) && weight.compareTo(toWeight) < 0) { // fromWeight <= weight < toWeight
                if (StringUtils.isBlank(skuTagCode) && StringUtils.isBlank(tagCodes)) {
                    matchConfigInfo = aliexpressConfigInfo;
                    break;
                }
                if (StringUtils.isBlank(tagCodes) || StringUtils.isBlank(skuTagCode)) {
                    continue;
                }

                List<String> skuLabels = CommonUtils.splitList(skuTagCode, ",");
                List<String> labels = CommonUtils.splitList(tagCodes, ",");
                //取交集
                labels.retainAll(skuLabels);

                if (CollectionUtils.isNotEmpty(labels)) {
                    matchConfigInfo = aliexpressConfigInfo;
                    break;
                }
            }
        }
        return matchConfigInfo;
    }

    /**
     * 根据重量和标签匹配算价配置,获取多条
     */
    public static List<AliexpressConfigInfo> getConfigsByMaxWeightAndTagCode(WeightTagCode weightTagCode, List<AliexpressConfigInfo> infoList) {
        if (weightTagCode == null || CollectionUtils.isEmpty(infoList)) {
            return Collections.emptyList();
        }
        Double weight = weightTagCode.getMaxWeight();
        //产品特殊标签
        String skuSpecialTags = weightTagCode.getSpecialTags();
        //产品标签
        String skuTagCode = weightTagCode.getTagCode();

        //重量判断
        Predicate<AliexpressConfigInfo> weightMatches = info -> {
            Double fromWeight = info.getFromWeight();
            Double toWeight = info.getToWeight() == null ? Double.MAX_VALUE : info.getToWeight();
            return !(weight.compareTo(fromWeight) < 0) && weight.compareTo(toWeight) < 0;
        };

        // 优先根据重量和特殊标签匹配
        List<AliexpressConfigInfo> matchConfigInfos = infoList.stream()
                .filter(info -> weightMatches.test(info) && hasCommonTags(info.getSpecialTagCode(), skuSpecialTags))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(matchConfigInfos)) {
            return matchConfigInfos;
        }

        //重量配置第一匹配,再根据标签匹配
        return infoList.stream()
                .filter(info -> weightMatches.test(info) && ((StringUtils.isBlank(info.getTagCodes())
                        && StringUtils.isBlank(skuTagCode)) || hasCommonTags(info.getTagCodes(), skuTagCode)))
                .collect(Collectors.toList());
    }

    /**
     * 检测两个标签是否有交集
     */
    public static boolean hasCommonTags(String tagCode1, String tagCode2) {
        if (StringUtils.isBlank(tagCode1) || StringUtils.isBlank(tagCode2)) {
            return false;
        }
        List<String> tagList1 = CommonUtils.splitList(tagCode1, ",");
        List<String> tagList2 = CommonUtils.splitList(tagCode2, ",");
        //取交集
        tagList1.retainAll(tagList2);
        return CollectionUtils.isNotEmpty(tagList1);
    }
    /**
     * 获取sku的最大重量和标签
     *
     * @param skuList sku列表
     * @param addWeight 增加重量
     * @return WeightTagCode
     */
    public static WeightTagCode getMaxWeightAndTagCode(List<String> skuList, Double addWeight) {
        if (CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        WeightTagCode weightTagCode1 = getSingleMaxWeightAndTagCode(skuList, addWeight);
        if (weightTagCode1 != null) {
            return weightTagCode1;
        }
        WeightTagCode weightTagCode2 = getSuiteMaxWeightAndTagCode(skuList, addWeight);
        if (weightTagCode2 != null) {
            return weightTagCode2;
        }
        return getComposeMaxWeightAndTagCode(skuList, addWeight);
    }
    public static WeightTagCode getComposeMaxWeightAndTagCode(List<String> skuList, Double addWeight) {
        WeightTagCode weightTagCode = new WeightTagCode();
        ComposeSku composeProduct = ProductUtils.getComposeProduct(skuList.get(0));
        if (null != composeProduct) {
            Double totalWeight = AliexpressWeightUtils.getMaxWeight(composeProduct, addWeight);
            String tagCode = aliexpressTemplateAttributeHelper.matchComposeTagCode(composeProduct.getTagCode());
            weightTagCode.setSku(composeProduct.getComposeSku());
            weightTagCode.setMaxWeight(totalWeight);
            weightTagCode.setTagCode(tagCode);
            return weightTagCode;
        }
        return null;
    }
    public static WeightTagCode getSuiteMaxWeightAndTagCode(List<String> skuList, Double addWeight) {
        SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(skuList.get(0));
        if (null != suiteSku) {
            WeightTagCode weightTagCode = new WeightTagCode();
            double totalWeight = AliexpressWeightUtils.getMaxWeight(suiteSku, addWeight);
            String tagCode = aliexpressTemplateAttributeHelper.matchComposeTagCode(suiteSku.getTagCode());
            weightTagCode.setSku(suiteSku.getSuiteSku());
            weightTagCode.setMaxWeight(totalWeight);
            weightTagCode.setTagCode(tagCode);
            return weightTagCode;
        }
        return null;
    }
    public static WeightTagCode getSingleMaxWeightAndTagCode(List<String> skuList, Double addWeight) {
        ProductInfo productInfo = singleItemEsService.getMaxWeightSingleItemBySonSkuList(skuList);
        if (productInfo != null) {
            WeightTagCode weightTagCode = new WeightTagCode();
            double totalWeight = AliexpressWeightUtils.getMaxWeight(productInfo, addWeight);
            weightTagCode.setSku(productInfo.getSonSku());
            weightTagCode.setMaxWeight(totalWeight);
            weightTagCode.setTagCode(productInfo.getEnTag());
            weightTagCode.setSpecialTags(StringUtils.join(productInfo.getSpecialTypeList(), ","));
            return weightTagCode;
        }
        return null;
    }
}