package com.estone.erp.publish.smt.bean.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.common.util.POIUtils;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.smt.model.AliexpressEsExtend;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * @Auther lc
 * @Date 2023年7月5日
 */
@Getter
@Setter
public class DownStateItem {

    @ExcelProperty(value = "销售组长")
    private String saleLeader;

    @ExcelProperty(value = "销售")
    private String sale;

    @ExcelProperty(value = "帐号")
    private String account;

    @ExcelProperty(value = "product id")
    private Long productId;

    @ExcelProperty(value = "标题")
    private String title;

    @ExcelProperty(value = "货号")
    private String articleNumber;

    @ExcelProperty(value = "sku")
    private String sku;

    @ExcelProperty(value = "价格")
    private Double price;

    @ExcelProperty(value = "库存")
    private Integer stock;

    @ExcelProperty(value = "商品状态")
    private String itemStatus;

    @ExcelProperty(value = "毛利")
    private String grossProfit;

    @ExcelProperty(value = "毛利率")
    private String grossProfitRate;

    @ExcelProperty(value = "店铺分组")
    private String shopGroup;

    @ExcelProperty(value = "运费分组")
    private String freightGrpup;

    @ExcelProperty(value = "SKU的重量+填充物重量+搭配包材重量")
    private Double skuWeight;

    @ExcelProperty(value = "sku销售成本价")
    private Double skuCost;

    @ExcelProperty(value = "SKU对应的包材重量")
    private Double skuPackingWeight;

    @ExcelProperty(value = "包材价格+填充物价格")
    private Double packingCost;

    @ExcelProperty(value = "产品在SMT后台填写的重量")
    private String itemWeight;

    @ExcelProperty(value = "单品状态")
    private String skuStatus;

    @ExcelProperty(value = "禁售平台")
    private String forbid;

    @ExcelProperty(value = "产品标签")
    private String itemLabel;

    @ExcelProperty(value = "类目id")
    private Integer categoryId;

    @ExcelProperty(value = "sku_id")
    private String skuId;

    @ExcelProperty(value = "调价方式")
    private String priceType;

    @ExcelProperty(value = "Russian Federation")
    private String ru;

    @ExcelProperty(value = "United States")
    private String us;

    @ExcelProperty(value = "Canada")
    private String ca;

    @ExcelProperty(value = "Spain")
    private String es;

    @ExcelProperty(value = "France")
    private String fr;

    @ExcelProperty(value = "United Kingdom")
    private String uk;

    @ExcelProperty(value = "Netherlands")
    private String nl;

    @ExcelProperty(value = "Israel")
    private String il;

    @ExcelProperty(value = "Brazil")
    private String br;

    @ExcelProperty(value = "Chile")
    private String cl;

    @ExcelProperty(value = "Australia")
    private String au;

    @ExcelProperty(value = "Ukraine")
    private String ua;

    @ExcelProperty(value = "Belarus")
    private String by;

    @ExcelProperty(value = "Japan")
    private String jp;

    @ExcelProperty(value = "Thailand")
    private String th;

    @ExcelProperty(value = "Singapore")
    private String sg;

    @ExcelProperty(value = "South Korea")
    private String kr;

    @ExcelProperty(value = "Indonesia")
    private String id;

    @ExcelProperty(value = "Malaysia")
    private String my;

    @ExcelProperty(value = "Philippines")
    private String ph;

    @ExcelProperty(value = "Vietnam")
    private String vn;

    @ExcelProperty(value = "Italy")
    private String it;

    @ExcelProperty(value = "Germany")
    private String de;

    @ExcelProperty(value = "Saudi Arabia")
    private String sa;

    @ExcelProperty(value = "United Arab Emirates")
    private String ae;

    @ExcelProperty(value = "Poland")
    private String pl;

    @ExcelProperty(value = "Turkey")
    private String tr;

    @ExcelProperty(value = "Portugal")
    private String pt;

    @ExcelProperty(value = "Belgium")
    private String be;

    @ExcelProperty(value = "Colombia")
    private String co;

    @ExcelProperty(value = "Mexico")
    private String mx;

    @ExcelProperty(value = "Morocco")
    private String ma;

    @ExcelProperty(value = "Switzerland")
    private String ch;

    @ExcelProperty(value = "Czech Republic")
    private String cz;

    @ExcelProperty(value = "New Zealand")
    private String nz;

    @ExcelProperty(value = "Lithuania")
    private String lt;

    @ExcelProperty(value = "Latvia")
    private String lv;

    @ExcelProperty(value = "Slovakia")
    private String sk;

    @ExcelProperty(value = "Norway")
    private String no;

    @ExcelProperty(value = "Hungary")
    private String hu;

    @ExcelProperty(value = "Bulgaria")
    private String bg;

    @ExcelProperty(value = "Estonia")
    private String ee;

    @ExcelProperty(value = "Romania")
    private String ro;

    @ExcelProperty(value = "Pakistan")
    private String pk;

    @ExcelProperty(value = "Croatia")
    private String hr;

    @ExcelProperty(value = "Nigeria")
    private String ng;

    @ExcelProperty(value = "Ireland")
    private String ie;

    @ExcelProperty(value = "Austria")
    private String at;

    @ExcelProperty(value = "Greece")
    private String gr;

    @ExcelProperty(value = "Sweden")
    private String se;

    @ExcelProperty(value = "Finland")
    private String fi;

    @ExcelProperty(value = "Denmark")
    private String dk;

    @ExcelProperty(value = "Slovenia")
    private String si;

    @ExcelProperty(value = "Malta")
    private String mt;

    @ExcelProperty(value = "Sri Lanka")
    private String lk;

    @ExcelProperty(value = "Luxembourg")
    private String lu;

    @ExcelProperty(value = "Peru")
    private String pe;

    @ExcelProperty(value = "Kuwait")
    private String kw;

    @ExcelProperty(value = "Qatar")
    private String qa;

    @ExcelProperty(value = "Oman")
    private String om;

    @ExcelProperty(value = "Bahrain")
    private String bh;

    @ExcelProperty(value = "Cyprus")
    private String cy;

    @ExcelProperty(value = "Ethiopia")
    private String et;

    @ExcelProperty(value = "Uganda")
    private String ug;

    @ExcelProperty(value = "South Africa")
    private String za;

    @ExcelProperty(value = "Kenya")
    private String ke;

    @ExcelProperty(value = "Ghana")
    private String gh;

    @ExcelProperty(value = "Algeria")
    private String dz;

    @ExcelProperty(value = "Serbia")
    private String srb;

    @ExcelProperty(value = "Reunion")
    private String re;

    @ExcelProperty(value = "Angola")
    private String ao;

    @ExcelProperty(value = "Iceland")
    private String is;

    @ExcelProperty(value = "Albania")
    private String al;

    @ExcelProperty(value = "Mauritius")
    private String mu;

    @ExcelProperty(value = "Mozambique")
    private String mz;

    @ExcelProperty(value = "United Republic of Tanzania")
    private String tz;

    @ExcelProperty(value = "El Salvador")
    private String sv;

    @ExcelProperty(value = "Maldives")
    private String mv;

    @ExcelProperty(value = "Cape Verde")
    private String cv;

    @ExcelProperty(value = "North Macedonia")
    private String mk;

    public DownStateItem(){

    };

    public DownStateItem(EsAliexpressProductListing product, AliexpressEsExtend extend){
        this.setSaleLeader(extend.getSalemanagerLeader());
        this.setSale(extend.getSalemanager());
        this.setAccount(product.getAliexpressAccountNumber());
        this.setProductId(product.getProductId());
        this.setTitle(POIUtils.transferStr2Str(product.getSubject()));
        this.setArticleNumber(product.getArticleNumber());
        this.setSku(product.getSkuCode());
        this.setPrice(product.getSkuPrice());
        this.setStock(product.getIpmSkuStock());
        this.setItemStatus(product.getProductStatusType());
        this.setGrossProfit(extend.getGrossProfit());
        this.setGrossProfitRate(extend.getGrossProfitMargin());
        this.setShopGroup(extend.getGroupNames());
        this.setFreightGrpup(extend.getFreightTemplateName());
        this.setSkuWeight(extend.getSkuTotalWeight());
        Double skuPurchasePrice = extend.getSkuPurchasePrice() == null ? 0.0 : extend.getSkuPurchasePrice() ;
        this.setSkuCost(BigDecimal.valueOf(skuPurchasePrice).setScale(3, RoundingMode.HALF_UP).doubleValue());
        this.setSkuPackingWeight(extend.getPmWeight());
        this.setPackingCost(extend.getPmPrice());
        this.setItemWeight(product.getGrossWeight());
        this.setSkuStatus(extend.getLifeCyclePhase());
        this.setForbid(extend.getForbiddenSaleChannel());
        this.setItemLabel(extend.getTags());
        this.setCategoryId(product.getCategoryId());
        this.setSkuId(product.getSkuId());
        this.setPriceType("percentage");
    }
}
