package com.estone.erp.publish.tiktok.controller;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.model.dto.ResultListDTO;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.tiktok.call.products.TiktokProductCall;
import com.estone.erp.publish.tiktok.componet.TiktokFeedTaskHelper;
import com.estone.erp.publish.tiktok.componet.TiktokSyncItemHelper;
import com.estone.erp.publish.tiktok.enums.TiktokOperateTypeEnum;
import com.estone.erp.publish.tiktok.job.TiktokSyncItemGlobalProductIdNullJobHandler;
import com.estone.erp.publish.tiktok.model.dto.TiktokGlobalItem;
import com.estone.erp.publish.tiktok.model.dto.TiktokProductCallRequest;
import com.estone.erp.publish.tiktok.util.TiktokLocalCacheUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

@RestController
@RequestMapping("/test/api")
public class TiktokApiTestContoller {

    @Resource
    private TiktokSyncItemHelper tiktokSyncItemHelper;

    @Resource
    private TiktokFeedTaskHelper tiktokFeedTaskHelper;

    @GetMapping("/test")
    public String test() {
        SaleAccountAndBusinessResponse saleAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, "Chenyao1-PH");
        tiktokSyncItemHelper.syncAllItem(saleAccount, new FeedTask());
        return "test";
    }

    @GetMapping("/getGlobalProduct")
    public ApiResult<?> getGlobalProduct() {
        SaleAccountAndBusinessResponse saleAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, "XieZhenghua-MY");
        saleAccount.setAccountNumber("XieZhenghua-MY");
        saleAccount.setClientId("686l6i24rlk7d");
        saleAccount.setClientSecret("a02c66633d00a2b264dd3707d1b6a59394148627");
        saleAccount.setColStr2("ROW_ClXvLQAAAABWuVjLFLpBolR2oWrwad5D");
        saleAccount.setAccessToken("ROW_9VkQ-QAAAABELrQ67Bkh7watm2CNr7V9uRZkS_MAv0mYZvRNkuh8-jQfnDsY5sngwtqKeqhZZ15VsMJgsEHm-w7liZZKybOwBfJrHrlLcEqsCRHoM4N1OIFL7DRQ6KfjyBWm7freKv-ITnCMxYfdvUwtEqgmxKLpSrUUmwgKyQ_OHiL-Sj6KYDXvOOEvde-6u9Eodz7DPCVA54qWUSswzpVVdvj8exG1");
        TiktokProductCall call = new TiktokProductCall(saleAccount);
        String itemDetail = call.getGlobalItemDetail("1730045431690398668");
        return ApiResult.newSuccess(itemDetail);
    }

    @GetMapping("/getGlobalProductList")
    public ApiResult<?> getGlobalProductList() {
        SaleAccountAndBusinessResponse saleAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, "XieZhenghua-MY");
        saleAccount.setAccountNumber("XieZhenghua-MY");
        saleAccount.setClientId("686l6i24rlk7d");
        saleAccount.setClientSecret("a02c66633d00a2b264dd3707d1b6a59394148627");
        saleAccount.setColStr2("ROW_ClXvLQAAAABWuVjLFLpBolR2oWrwad5D");
        saleAccount.setAccessToken("ROW_9VkQ-QAAAABELrQ67Bkh7watm2CNr7V9uRZkS_MAv0mYZvRNkuh8-jQfnDsY5sngwtqKeqhZZ15VsMJgsEHm-w7liZZKybOwBfJrHrlLcEqsCRHoM4N1OIFL7DRQ6KfjyBWm7freKv-ITnCMxYfdvUwtEqgmxKLpSrUUmwgKyQ_OHiL-Sj6KYDXvOOEvde-6u9Eodz7DPCVA54qWUSswzpVVdvj8exG1");
        TiktokProductCall call = new TiktokProductCall(saleAccount);
        TiktokProductCallRequest request = new TiktokProductCallRequest();
        request.setSellerSkus(Lists.newArrayList("4NB403081-C_AA"));

        ResultListDTO<TiktokGlobalItem> allGlobalItems = call.getAllGlobalItems(10, null, request);
        return ApiResult.newSuccess(allGlobalItems);
    }

    @GetMapping("/updateGlobalProductId")
    public ApiResult<?> updateGlobalProductId() {
        // 1 首先，根据 sellerSku 查询 globalProductId
        SaleAccountAndBusinessResponse saleAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, "XieZhenghua-MY");
        saleAccount.setAccountNumber("XieZhenghua-MY");
        saleAccount.setClientId("686l6i24rlk7d");
        saleAccount.setClientSecret("a02c66633d00a2b264dd3707d1b6a59394148627");
        saleAccount.setColStr2("ROW_ClXvLQAAAABWuVjLFLpBolR2oWrwad5D");
        saleAccount.setAccessToken("ROW_9VkQ-QAAAABELrQ67Bkh7watm2CNr7V9uRZkS_MAv0mYZvRNkuh8-jQfnDsY5sngwtqKeqhZZ15VsMJgsEHm-w7liZZKybOwBfJrHrlLcEqsCRHoM4N1OIFL7DRQ6KfjyBWm7freKv-ITnCMxYfdvUwtEqgmxKLpSrUUmwgKyQ_OHiL-Sj6KYDXvOOEvde-6u9Eodz7DPCVA54qWUSswzpVVdvj8exG1");
        TiktokProductCall call = new TiktokProductCall(saleAccount);
        TiktokProductCallRequest request = new TiktokProductCallRequest();
        request.setSellerSkus(Lists.newArrayList("4NB403081-C_AA"));
        ResultListDTO<TiktokGlobalItem> allGlobalItems = call.getAllGlobalItems(10, null, request);

        // 获取到 globalProductId了 ，再获取产品的相关id
        List<TiktokGlobalItem> list = allGlobalItems.getList();
        Map<Long, Map<String, String>> globalProductAndRegionProductIdMap = new HashMap<Long, Map<String, String>>();
        for (TiktokGlobalItem tiktokGlobalItem : list) {
            Map<String, String> regionAndProductIdMap = call.getRegionAndProductIdMap(tiktokGlobalItem.getGlobalProductId());
            globalProductAndRegionProductIdMap.put(Long.valueOf(tiktokGlobalItem.getGlobalProductId()), regionAndProductIdMap);
        }
        // 拿到 globalProductAndRegionProductIdMap 就可以根据 productId来更新 globalProductId

        return ApiResult.newSuccess(globalProductAndRegionProductIdMap);
    }

    @Autowired
    private TiktokSyncItemGlobalProductIdNullJobHandler tiktokSyncItemGlobalProductIdNullJobHandler;

    @GetMapping("/")
    public ApiResult<?> testTiktokSyncItemGlobalProductIdNullJobHandler() {
        TiktokSyncItemGlobalProductIdNullJobHandler.Params params = new TiktokSyncItemGlobalProductIdNullJobHandler.Params();
        try {
            params.setAccountNumberList(Lists.newArrayList("XieZhenghua-TH"));
            tiktokSyncItemGlobalProductIdNullJobHandler.run(JSON.toJSONString(params));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ApiResult.newSuccess(params);
    }

    @GetMapping
    public ApiResult<?> testTiktokAddSync() {
        SaleAccountAndBusinessResponse saleAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, "Haoneng5jin-MY");
        String updatedGe = "2024-04-01 00:00:00";
        String updatedLe = "2024-05-23 23:53:43";
        Date dateGe = DateUtils.parseDate(updatedGe, "yyyy-MM-dd HH:mm:ss");
        Date dateLe = DateUtils.parseDate(updatedLe, "yyyy-MM-dd HH:mm:ss");
        Boolean needRetry = false;

        FeedTask feedTask = tiktokFeedTaskHelper
                .initIncrementSyncFeedTask(SaleChannel.CHANNEL_TIKTOK, "Haoneng5jin-MY",
                        TiktokOperateTypeEnum.SYNC_ITEM.getStatusMsgEn(), updatedGe, updatedLe);
        tiktokSyncItemHelper.syncAddItem(saleAccount, feedTask, dateGe, dateLe, needRetry);

        return ApiResult.newSuccess(feedTask);
    }

    @GetMapping("/getUnPublishSystemCategory")
    public ApiResult<?> getCategory() {
        List<String> unPublishSystemCategory = TiktokLocalCacheUtil.getUnPublishSystemCategory();
        if (null == unPublishSystemCategory) {
            SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
            SystemParam systemParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_TIKTOK, "TIKTOK_FORBIDDEN_CATEGORY", "TIKTOK_FORBIDDEN_CATEGORY");
            String systemParamValue = "";
            if(null != systemParam) {
                systemParamValue = systemParam.getParamValue();
            }
            if (StringUtils.isBlank(systemParamValue)) {
                unPublishSystemCategory = new ArrayList<>();
                TiktokLocalCacheUtil.setUnPublishSystemCategory(new ArrayList<>());
            } else {
                unPublishSystemCategory = Arrays.asList(systemParamValue.split(","));
                TiktokLocalCacheUtil.setUnPublishSystemCategory(unPublishSystemCategory);
            }
        }
        return ApiResult.newSuccess(unPublishSystemCategory);
    }
}
