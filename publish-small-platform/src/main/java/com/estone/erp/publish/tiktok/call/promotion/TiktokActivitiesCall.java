package com.estone.erp.publish.tiktok.call.promotion;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tiktok.call.AbstractTiktokCall;
import com.estone.erp.publish.tiktok.call.promotion.domain.request.SearchActivitiesRequest;
import com.estone.erp.publish.tiktok.constant.TiktokCallConstant;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;

public class TiktokActivitiesCall extends AbstractTiktokCall {

    public TiktokActivitiesCall(SaleAccountAndBusinessResponse tiktokAccount) {
        super(tiktokAccount);
    }


    public ApiResult<String> searchActivities(SearchActivitiesRequest request) {
        JSONObject bodyParam = JSONObject.parseObject(JSON.toJSONString(request));
        HttpPost tiktokPostRequest = this.createTiktokPostRequest(TiktokCallConstant.SEARCH_ACTIVITIES_URL, null, bodyParam, true);
        String body = this.execute(tiktokPostRequest);
        return ApiResult.newSuccess(body);
    }

    public ApiResult<String> getActivitiesDetail(String activityId) {
        HttpGet tiktokGetRequest = this.createTiktokGetRequest(TiktokCallConstant.GET_ACTIVITIES_DETAIL_URL.replace("%s", activityId), null, true);
        String body = this.execute(tiktokGetRequest);
        return ApiResult.newSuccess(body);
    }
}
