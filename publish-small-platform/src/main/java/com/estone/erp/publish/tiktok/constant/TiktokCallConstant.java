package com.estone.erp.publish.tiktok.constant;

/**
 * <AUTHOR>
 * @date 2023/10/31 15:55
 */
public class TiktokCallConstant {

    /**
     * 路径分隔符
     */
    public static final String PATH_SPLIT = "/";

    /**
     * 基础地址
     */
    public static final String ROOT_URL = "https://open-api.tiktokglobalshop.com";

    /**
     * 搜索商品
     */
    public static final String SEARCH_ITEM_URL = "/product/202309/products/search";

    /**
     * 商品详情
     */
    public static final String ITEM_DETAIL_URL = "/product/202309/products/%s";

    /**
     * 获取分类
     */
    public static final String GET_CATEGORY_URL = "/product/202309/categories";

    /**
     * 修改库存
     */
    public static final String UPDATE_INVENTORY_URL = "/product/202309/products/%s/inventory/update";

    /**
     * 修改价格
     */
    public static final String UPDATE_PRICE_URL = "/product/202309/products/%s/prices/update";

    /**
     * 获取全球分类
     */
    public static final String GET_GLOBAL_CATEGORY_URL = "/product/202309/global_categories";

    /**
     * 获取全球推荐分类
     */
    public static final String GET_GLOBAL_RECOMMEND_CATEGORY_URL = "/product/202309/global_categories/recommend";

    /**
     * 获取全球属性
     */
    public static final String GET_ATTRIBUTES_URL = "/product/202309/categories/%s/global_attributes";

    /**
     * 上传图片
     */
    public static final String UPLOAD_IMAGE_URL = "/product/202309/images/upload";

    /**
     * 上传文件
     */
    public static final String UPLOAD_FILE_URL = "/product/202309/files/upload";

    /**
     * 创建全球产品
     */
    public static final String CREATE_PRODUCT_URL = "/product/202309/global_products";

    /**
     * 发布全球产品
     */
    public static final String PUBLISH_PRODUCT_URL = "/product/202309/global_products/%s/publish";

    /**
     * 获取webhooks
     */
    public static final String GET_SHOP_WEBHOOKS_URL = "/event/202309/webhooks";

    /**
     * 停用产品
     */
    public static final String DEACTIVATE_PRODUCTS_URL = "/product/202309/products/deactivate";

    /**
     * 删除产品
     */
    public static final String DELETE_PRODUCTS_URL = "/product/202309/products";

    /**
     * 获取全球商品
     */
    public static final String GET_GLOBAL_PRODUCT = "/product/202309/global_products/%s";

    /**
     * 获取全球商品列表
     */
    public static final String SEARCH_GLOBAL_ITEM_URL = "/product/202312/global_products/search";

    /**
     * 获取仓库
     */
    public static final String GET_WAREHOUSES = "/logistics/202309/warehouses";

}
