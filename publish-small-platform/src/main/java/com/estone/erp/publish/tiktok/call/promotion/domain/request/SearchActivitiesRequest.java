package com.estone.erp.publish.tiktok.call.promotion.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class SearchActivitiesRequest {

    @JsonProperty("status")
    private String status;
    @JsonProperty("activity_title")
    private String activityTitle;
    @JsonProperty("page_size")
    private Integer pageSize;
    @JsonProperty("page_token")
    private String pageToken;
    @JsonProperty("activity_type")
    private String activityType;
}
