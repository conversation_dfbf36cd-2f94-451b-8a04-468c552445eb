package com.estone.erp.publish.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpStatus;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/2 11:50
 */
@Slf4j
public abstract class AbstractHttpCall {

    /**
     * 请求客户端
     */
    protected CloseableHttpClient httpClient;

    /**
     * 构造方法
     */
    public AbstractHttpCall() {
        RequestConfig defaultRequestConfig = RequestConfig.custom().setConnectionRequestTimeout(2 * 60 * 1000).setSocketTimeout(2 * 60 * 1000).setConnectTimeout(60 * 1000).build();

        this.httpClient = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).setMaxConnPerRoute(100).setMaxConnTotal(400).build();
    }

    /**
     * 创建GET请求
     */
    protected HttpGet createGetRequest(String url) {
        return this.createGetRequest(url, null);
    }

    /**
     * 创建GET请求
     */
    protected HttpGet createGetRequest(String url, Map<String, Object> data, Object... args) {
        // 格式化请求地址
        if (args != null) {
            url = String.format(url, args);
        }

        if (MapUtils.isNotEmpty(data)) {
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();

                if (StringUtils.isBlank(key)) {
                    continue;
                }

                Object value = entry.getValue();

                if (value == null) {
                    continue;
                }

                url += url.contains("?") ? "&" : "?";

                url += key + "=" + value;
            }
        }

        return new HttpGet(url);
    }

    /**
     * 创建POST请求
     */
    protected HttpPost createPostRequest(String url) {
        return this.createPostRequest(url, null);
    }

    /**
     * 创建POST请求
     */
    protected HttpPost createPostRequest(String url, JSONObject data, Object... args) {
        // 格式化请求地址
        if (args != null) {
            url = String.format(url, args);
        }

        // 创建请求对象
        HttpPost request = new HttpPost(url);

        // 设置请求实体
        if (null != data) {
            request.setEntity(new StringEntity(JSON.toJSONString(data, SerializerFeature.DisableCircularReferenceDetect), StandardCharsets.UTF_8));
        }

        return request;
    }

    /**
     * 创建delete请求
     */
    protected HttpDeleteWithBody createDeleteRequest(String url, JSONObject data, Object... args) {
        // 格式化请求地址
        if (args != null) {
            url = String.format(url, args);
        }

        // 创建请求对象
        HttpDeleteWithBody request = new HttpDeleteWithBody(url);

        // 设置请求实体
        if (null != data) {
            request.setEntity(new StringEntity(JSON.toJSONString(data, SerializerFeature.DisableCircularReferenceDetect), StandardCharsets.UTF_8));
        }

        return request;
    }

    /**
     * 创建PUT请求
     */
    protected HttpPut createPutRequest(String url) {
        return this.createPutRequest(url, null);
    }

    protected HttpPatch createPatchRequest(String url, JSONObject data, Object... args) {
        // 格式化请求地址
        if (args != null) {
            url = String.format(url, args);
        }

        // 创建请求对象
        HttpPatch request = new HttpPatch(url);

        // 设置请求实体
        request.setEntity(new StringEntity(JSONObject.toJSONString(data), StandardCharsets.UTF_8));

        return request;
    }

    /**
     * 创建PUT请求
     */
    protected HttpPut createPutRequest(String url, Map<String, Object> data, Object... args) {
        // 格式化请求地址
        if (args != null) {
            url = String.format(url, args);
        }

        // 创建请求对象
        HttpPut request = new HttpPut(url);

        // 设置请求实体
        if (MapUtils.isNotEmpty(data)) {
            request.setEntity(new StringEntity(JSON.toJSONString(data), "UTF-8"));
        }

        return request;
    }

    /**
     * 设置请求实体
     */
    private void setRequestEntity(HttpEntityEnclosingRequest request, Map<String, Object> data) {
        if (MapUtils.isEmpty(data)) {
            return;
        }

        List<NameValuePair> parameters = new ArrayList<>();

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String key = entry.getKey();

            if (StringUtils.isBlank(key)) {
                continue;
            }

            Object value = entry.getValue();

            if (value == null) {
                continue;
            }

            if (value instanceof Collection) {
                for (Object obj : (Collection<?>) value) {
                    if (obj == null) {
                        continue;
                    }

                    parameters.add(new BasicNameValuePair(key, obj.toString()));
                }
            } else if (value instanceof Map) {
                for (Object paramKey : ((Map) value).keySet()) {
                    Object paramValue = ((Map) value).get(paramKey);
                    if (paramKey== null || paramValue == null) {
                        continue;
                    }

                    parameters.add(new BasicNameValuePair(paramKey.toString(), paramValue.toString()));
                }
            } else {
                parameters.add(new BasicNameValuePair(key, value.toString()));
            }
        }

        if (CollectionUtils.isEmpty(parameters)) {
            return;
        }

        try {
            request.setEntity(new UrlEncodedFormEntity(parameters, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    protected String execute(HttpUriRequest request) {
        // 响应对象
        CloseableHttpResponse response = null;

        try {
            // 执行请求
            response = httpClient.execute(request);

            if (null != response.getStatusLine()) {
                int code = response.getStatusLine().getStatusCode();
                if (code >= HttpStatus.BAD_REQUEST.value()) {
                    String message = String.format("[%s %s] %s", code, response.getStatusLine().getReasonPhrase(),
                            EntityUtils.toString(response.getEntity()));
                    throw new RuntimeException(message);
                }
                if (code == HttpStatus.NO_CONTENT.value()) {
                    return null;
                }
            }

            // 返回响应报文
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            HttpClientUtils.closeQuietly(response);
        }
    }
}
